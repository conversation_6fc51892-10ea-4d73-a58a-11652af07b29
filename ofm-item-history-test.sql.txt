WITH supplier_data AS (
  SELECT 
    po.id as po_id,
    CASE 
      WHEN po.supplier_name IS NOT NULL THEN po.supplier_name
      WHEN po.supplier_type = 'company' THEN c.name
      WHEN po.supplier_type = 'project' THEN p.name
      WHEN po.supplier_type = 'supplier' THEN s.name
      ELSE 'N/A'
    END as supplier_name
  FROM purchase_orders po
  LEFT JOIN companies c ON po.supplier_id = c.id AND po.supplier_type = 'company'
  LEFT JOIN projects p ON po.supplier_id = p.id AND po.supplier_type = 'project'
  LEFT JOIN suppliers s ON po.supplier_id = s.id AND po.supplier_type = 'supplier'
) SELECT 
  poi.id,
  CONCAT('RS-', CASE WHEN LENGTH(CAST(r.company_code AS VARCHAR)) = 1 THEN CONCAT('0', r.company_code) ELSE CAST(r.company_code AS VARCHAR) END, r.rs_letter, r.rs_number) as rs_number,
  sd.supplier_name,
  CASE 
    WHEN cis.discount_type = 'percent' THEN 
      ROUND(CAST(cis.unit_price * (1 - cis.discount_value / 100) AS numeric), 2)
    ELSE 
      ROUND(CAST(cis.unit_price - (cis.discount_value / poi.quantity_purchased) AS numeric), 2)
  END as unit_price,
  ril.quantity as requested_quantity,
  cis.quantity as approved_quantity,
  poi.quantity_purchased,
  po.created_at as date_purchased,
  r.id as requisition_id
FROM purchase_order_items poi
JOIN purchase_orders po ON poi.purchase_order_id = po.id
JOIN requisition_item_lists ril ON poi.requisition_item_list_id = ril.id
JOIN items i ON ril.item_id = i.id
JOIN requisitions r ON po.requisition_id = r.id
LEFT JOIN canvass_item_suppliers cis ON poi.canvass_item_supplier_id = cis.id
JOIN supplier_data sd ON po.id = sd.po_id
WHERE po.status IN ('for_sending', 'for_delivery', 'closed_po', 'cancelled_po')
AND i.id = 1
ORDER BY rs_number ASC, date_purchased DESC