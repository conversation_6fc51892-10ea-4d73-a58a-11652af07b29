async function templateRoute(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const { PERMISSIONS } = fastify.diScope.resolve('constants').permission;
  const rsPaymentRequestController = fastify.diScope.resolve(
    'rsPaymentRequestController',
  );
  const nonRequisitionController = fastify.diScope.resolve(
    'nonRequisitionController',
  );
  const deliveryReceiptController = fastify.diScope.resolve(
    'deliveryReceiptController',
  );
  const invoiceReportController = fastify.diScope.resolve(
    'invoiceReportController',
  );
  const requisitionController = fastify.diScope.resolve(
    'requisitionController',
  );
  const canvassController = fastify.diScope.resolve('canvassController');
  const purchaseOrderController = fastify.diScope.resolve(
    'purchaseOrderController',
  );
  const templateController = fastify.diScope.resolve('templateController');
  const gatePassController = fastify.diScope.resolve('gatePassController');

  fastify.route({
    method: 'GET',
    url: '/',
    handler: templateController.generateTemplate.bind(templateController),
  });

  fastify.route({
    method: 'GET',
    url: '/requisition/:id',
    handler: requisitionController.createRequisitionPdf.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/non-requisitions/:id',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.VIEW_NON_RS_PAYMENTS),
    ]),
    schema: {
      params: entities.nonRequisition.nonRsParams,
    },
    handler: nonRequisitionController.generatePdf.bind(
      nonRequisitionController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/delivery-receipt/:id',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.VIEW_DELIVERY_RECEIPTS),
    ]),
    schema: {
      params: entities.deliveryReceipt.getDeliveryReceiptByIdSchema,
    },
    handler: deliveryReceiptController.generatePdf.bind(
      deliveryReceiptController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/invoice-report/:id',
    schema: {
      params: entities.invoiceReport.getInvoiceReportByIdSchema,
    },
    handler: invoiceReportController.generatePdf.bind(invoiceReportController),
  });

  fastify.route({
    method: 'GET',
    url: '/canvass/:id',
    schema: {
      params: entities.canvass.getCanvassParams,
    },
    handler: canvassController.generatePdf.bind(canvassController),
  });

  fastify.route({
    method: 'GET',
    url: '/dashboard',
    handler: requisitionController.createDashboardPdf.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/purchase-order/:id',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderPDFByIdParams,
    },
    handler: purchaseOrderController.generatePurchaseOrderPdf.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/rs-payment-request/:id',
    schema: {
      params: entities.rsPaymentRequest.paymentRequestIdParams,
    },
    handler: rsPaymentRequestController.generateRsPaymentRequestPdf.bind(
      rsPaymentRequestController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/gate-pass/:id',
    schema: {
      params: entities.rsPaymentRequest.paymentRequestIdParams,
    },
    handler: gatePassController.generateGatePass.bind(gatePassController),
  });
}

module.exports = templateRoute;
