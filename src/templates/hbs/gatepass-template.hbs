<html lang='en'>

<head>
    <meta charset='UTF-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <title>Purchase Request System</title>
    <link rel='preconnect' href='https://rsms.me/' />
    <link rel='stylesheet' href='https://rsms.me/inter/inter.css' />
    <style>
        @page {
            size: legal;
            margin: 0.3cm;
        }

        .row {
            display: flex;
            align-items:
                center;
            flex-direction: row;
        }

        body {
            font-family: 'Inter', sans-serif;
            font-size: 12px;
            line-height: 1.2;
            color: #000;
            margin: 0;
            padding: 0;
            width: 8.5in;
            height: 11in;
            box-sizing: border-box;
        }

        /* Support for Inter
      variable font */
        @supports (font-variation-settings: normal) {
            body {
                font-family: 'Inter var', sans-serif;
            }
        }

        .wrapper {
            max-width: 7.5in;
            margin: 0 auto;
            padding: 0;
            padding-top: 20;
        }

        .header {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
        }

        .po-title {
            margin-top:
                10px;
            font-size: 12px;
            color: #754445;
        }

        .logo {
            font-size: 20px;
            line-height: 1;
        }

        .title {
            font-weight: bold;
            font-size: 12px;
            color:
                #800000;
        }

        .page-info {
            text-align: right;
            font-size: 11px;
        }

        .po-section {
            font-size: 14px;
        }

        .date-extracted-section {
            text-align: right;
            font-size: 8px;

            span {
                margin-right: 100px;
            }
        }

        .project-company-header {
            font-weight: bold;
            display: flex;
            margin-top: 10px;
            justify-content:
                center;
            align-items: center;
            text-align: center;
            width: 100%;
            border-bottom: 1px solid #000;
        }

        .pr-number {
            margin-top: 10px;
            display:
                flex;
            align-items: center;
            justify-content: left;
            /*gap: 10px;*/
        }

        .pr-label {
            font-size: 14px;
            font-weight: bold;
        }

        .pr-value {
            text-decoration: underline;
            font-weight: bold;
        }

        .date-section {
            display:
                flex;
            align-items: center;
        }

        .date-label {
            color: #323232;
            font-size: 7px;
            margin-left: 30px;
        }

        .date-value {
            font-size: 8px;
            font-weight: bold;
        }

        .note-section {
            font-size: 8px;
            margin-top: 10px;
        }

        .bottom-note-section {
            font-size: 8px;
            margin-top: 10px;
            text-align: right;
            font-style: italic;
            width: 100%;
            margin-bottom: 25px;
        }

        .amount-words {
            font-style: italic;
            font-weight: bold;
        }

        .bottom-note-section input {
            border: none;
            width:
                30%;
            font-size: 8px;
            margin-left: 5px;
            font-style: italic;
        }

        .section {
            margin: 10px 0;
            display: flex;
            gap: 10px;

            .details-grid-outer {
                flex:
                    100%;

                .details-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr 1fr 1fr;
                    gap: 10px;
                    font-size: 10px;
                    padding-bottom: 10px;
                }
            }

            .section-terms {
                flex: 70%;
                flex-basis: 70%;

                .details-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 10px;
                    font-size: 10px;
                    padding-bottom: 10px;
                }
            }

            .section-purchasing {
                flex: 30%;
                flex-basis:
                    30%;
            }

            .details-grid-outer2 {
                border: 1px solid #ccc;
                border-radius: 10px;
                padding: 10px;
                padding-bottom: 0px;
            }
        }

        .section-title {
            font-weight:
                bold;
            margin-bottom: 10px;
            font-size: 12px;
        }

        .section-description {
            margin-bottom: 5px;
            font-size: 9px;
        }

        .details-grid-outer {
            border: 1px solid #ccc;
            border-radius: 10px;
            padding: 10px;
            padding-bottom: 0px;
        }

        .signing-details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 10px;
            padding: 10px;
            font-size: 10px;
        }

        .details-row {
            display:
                flex;
            flex-direction: column;
        }

        .details-label {
            color: #323232;
            font-weight: normal;
            font-size: 9.5px;
            margin-bottom: 4px;
        }

        .signing-details-label {
            color: #323232;
            font-weight: normal;
            font-size:
                8px;
        }

        .signing-details-label input {
            margin-top: 20px;
            border-bottom:
                solid 1px;
            width: 150px;
        }

        .checks-container {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 10px;
            width: 100%;
            padding:
                0px 0 10px;
        }

        .check-item {
            display: flex;
            align-items: center;
            white-space: nowrap;
        }

        .check-item label {
            font-size: 8px;
            margin-right:
                3px;
        }

        /* Style for the checkboxes */
        .check-item input[type='checkbox'] {
            width: 10px;
            height: 10px;
            margin-right: 3px;
        }

        .check-textfield {
            border:
                none;
            border-bottom: 1px solid #000;
            font-size: 7px;
            padding: 0;
            margin:
                0;
        }

        input,
        select {
            border: none;
            background: transparent;
            width: 100%;
            font-family: inherit;
            font-size: inherit;
            outline: none;
        }

        .items-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
        }

        .items-title {
            font-weight: bold;
        }

        .table-container {
            border-radius: 8px;
            overflow: hidden;
            border: 1.8px solid #222222;
            margin-top:
                5px;
            margin-bottom: 15px;
            margin-top: 15px;
            padding: 1px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            border: none;
        }

        th,
        td {
            border-right: 1px solid #ccc;
            padding: 5px;
            text-align:
                left;
            font-size: 10px;
            font-weight: normal;
            color: #4F575E !important;
        }

        /* Remove border from outer edges of table cells */
        tr:first-child th {
            border-top: none;
            color: #272B30 !important;
        }

        tr:last-child td {
            border-bottom: none;
            color: #272B30 !important;
        }

        th:first-child,
        td:first-child {
            border-left: none;
        }

        th:last-child,
        td:last-child {
            border-right: none;
        }

        th,
        td {
            padding: 8px;
            text-align: left;
            font-size:
                10px;
        }

        th {
            border-bottom:
                1px solid #ccc;
            color: #4f575e;
            font-weight: bold;
            vertical-align: top;
            text-align: left;
            font-size: 10px;
        }

        td {
            border-bottom: none !important;
            height: 30px;
        }

        .base-col {
            width: 80px;
        }

        .num-col {
            width: 50px;
        }

        .unit-col {
            width: 30px;
        }

        .discount-col {
            width: 40px;
        }

        .pricing-col {
            width: 60px;
        }

        .pricing-col input {
            width:
                90%;
            margin-right: 5px;
        }

        .item-name-cell {
            vertical-align: center;
            word-break: break-all;
            word-wrap: break-word;
            white-space: normal;
            padding: 4px;
            width: 400px;
            height: 57px;
        }

        .item-name-input {
            color: #4f575e;
            width:
                100%;
            outline: none;
            border: none;
            background: transparent;
            font-family:
                inherit;
            font-size: inherit;
            text-decoration: underline;
            word-break:
                break-all;
            word-wrap: break-word;
            white-space: normal;
            text-align: left;
            resize: none;
            overflow: hidden;
        }

        .text-left {
            text-align: left;
        }

        .text-right {
            text-align: right;
        }

        .footer {
            margin-top: 330px;
            text-align: left;
            font-size: 10px;
            color: #666;
        }

        .main-title {
            text-align: center;
            font-size: 14px;
            grid-column: 2 / 4;

            span {
                font-weight: bold;
                display: inline-block;
                height: 10px;
            }

            div {
                font-size:
                    7px;

                .input {
                    display: inline-block;
                    width: 80px;
                }
            }
        }

        .receiving-report-title {
            margin-top: 10px;
            font-weight: bold;
            font-size:
                17px;
        }

        .logo-title {
            font-size: 19px;
            color: #754445;
            font-weight: bold;
            text-align: center;
        }

        .company-title {
            font-size: 16px;
        }

        .company-sub-title {
            font-size: 14px;
            margin-top: 10px;
        }


        .main-header {
            text-align: center;
            grid-column: 2 / 5;

            input {
                display: inline-flex;
                width: 100px;
            }
        }

        .sub-header {
            font-size: 9px;
        }

        .page-wrapper {
            text-align: right;
            font-size: 12px;
        }

        .header-title {
            font-weight: bold;
            font-size: 14px;
            width: 150px;
            color: #420001;
        }

        .header-title-2 {
            margin-left: 10px;
            font-weight: bold;
            font-size: 14px;
            color: #420001;
        }

        .header-company {
            color: #131313;
        }

        .page-wrapper {
            margin-top: 5px;
            text-align: right;
            font-size: 12px;
        }

        .page-wrapper-2 {
            margin-top: 5px;
            text-align: right;
            font-size: 12px;
        }

        .header-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
        }

        .sub-title {
            font-weight: bold;
        }

        .flex {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }

        .flex-start {
            display: flex;
            justify-content: flex-start;
            margin-top: 10px;
        }

        .flex-end {
            display: flex;
            justify-content: flex-end;
            margin-top: 10px;
        }


        .flex-grow {
            flex-grow: 1;
        }

        .label {
            width: 100px;
        }

        .label-2 {
            width: 50px;
        }

        .mr5 {
            margin-left: 10px;
        }

        .base-long-col {
            width: 250px;
        }

        .center {
            text-align: center;
        }

        .sign-label {
            margin-bottom: 40px;
        }

        .sign-name {
            text-align: center;
            padding-top: 5px;
            border-top: 1px solid black;
        }

        .sign-wrapper {
            width: 170px;
        }

        .double-hr {
            margin-top: 20px;
            height: 3px;
            border-top: 2px solid black;
            border-bottom: 2px solid black;

        }
    </style>
</head>

<body>
    <div class='wrapper'>

        {{#each pagesData as |pageData pageIndex|}}
        <div class='wrapper'>

            {{#if @first}}
            <div class='header'>
                <div class='logo'>
                    <svg xmlns='http://www.w3.org/2000/svg' width='72' height='72' viewBox='0 0 72 72' fill='none'>
                        <path d='M17.5918 27.7472H16.6144V42.9498H17.5918V27.7472Z' fill='#653332' />
                        <path fill-rule='evenodd' clip-rule='evenodd'
                            d='M43.1104 27.7472H44.1963L47.6712 42.9498H46.6396L45.608 38.1718H41.6444L40.6128 42.9498H39.6355L43.1104 27.7472ZM43.7076 29.376H43.599L41.9543 36.8688H45.3067L43.7076 29.376Z'
                            fill='#653332' />
                        <path
                            d='M28.4508 42.9498H29.2109V37.2488L32.1428 27.7472H31.1655L28.8308 35.62L26.4961 27.7472H25.5731L28.4508 37.2488V42.9498Z'
                            fill='#653332' />
                        <path d='M35.0748 27.7472H34.0974V42.9498H38.2239V41.5381H35.0748V27.7472Z' fill='#653332' />
                        <path fill-rule='evenodd' clip-rule='evenodd'
                            d='M59.6161 42.9498V27.7472C59.6161 27.7472 61.4622 27.7472 62.6024 27.7472C63.2539 27.7472 63.9055 27.9878 65.0765 29.1589C66.8495 30.9319 66.7831 35.3485 66.7831 35.3485C66.7831 35.3485 66.946 39.0406 65.3171 41.3753C64.2768 42.8664 62.3852 42.9498 62.3852 42.9498H59.6161ZM60.3763 41.5381V29.2675C60.3763 29.2675 62.0594 29.2675 63.0367 29.2675C63.1996 29.2675 64.042 29.376 65.0765 31.1678C65.9403 32.6639 65.9144 35.4028 65.9144 35.4028C65.9144 35.4028 65.9403 38.2503 65.0765 39.7464C64.042 41.5381 62.8196 41.5381 62.8196 41.5381H60.3763Z'
                            fill='#653332' />
                        <path
                            d='M50.4946 27.7472H49.7344V42.9498H50.4946V29.7018L55.3811 42.9498H56.467V27.7472H55.5983V40.9409L50.7117 27.7472H50.4946Z'
                            fill='#653332' />
                        <path
                            d='M22.4783 27.7472H21.6096H19.7635V29.2675H21.6096V42.9498H22.4783V29.2675H24.3243V27.7472H22.4783Z'
                            fill='#653332' />
                        <path
                            d='M9.88181 43.2756C7.16706 43.2756 5.21243 39.7265 5.21243 35.3485C5.21243 30.9705 7.30298 27.53 9.88181 27.53C11.6754 27.53 13.2581 29.1381 14.0082 31.6565H12.9112C12.2505 30.0142 11.1618 28.8331 9.88181 28.8331C7.78841 28.8331 6.18974 31.8392 6.18974 35.3776C6.18974 38.916 7.67807 41.8639 9.88181 41.8639C11.119 41.8639 12.1768 40.7534 12.8434 39.2035H13.9539C13.1882 41.6326 11.6364 43.2756 9.88181 43.2756Z'
                            fill='#653332' />
                        <path fill-rule='evenodd' clip-rule='evenodd'
                            d='M36.1064 2.88C44.343 2.88 49.4087 10.3727 53.3396 22.3176H41.1015C40.1747 12.4853 38.386 5.05181 36.4321 5.05181C34.2082 5.05182 31.0037 12.7915 29.8282 24.9238H18.4994C21.0036 11.8618 27.8697 2.88 36.1064 2.88ZM18.3657 45.8817C20.8297 59.3469 28.1281 69.12 36.1064 69.12C42.9722 69.12 49.867 61.1975 53.0801 49.791H41.0009C39.8694 59.3941 37.7495 65.9166 35.9435 65.9166C34.2775 65.9166 31.4297 57.4818 30.1339 45.8817H18.3657Z'
                            fill='#653332' />
                    </svg>

                </div>
                <div class='main-header'>
                    <div class='logo-title'>CITYLAND GROUP OF COMPANIES</div>
                    <div class='company-title'><b>GATE PASS</b></div>
                    <div class='company-sub-title'><b>(INTERNAL USE ONLY)</b></div>
                </div>
            </div>
        </div>

        <div>
            <div class='project-company-header'></div>
        </div>

        <div class="flex">
            <div class="flex-grow">
                <div class="flex-start">
                    <div class="label">DATE</div>:
                    <div class="mr5">{{@root.issuedDate}}</div>
                </div>
            </div>
            <div class="flex-grow">
                <div class="flex-end">
                    <div class="label"><b>GATE PASS NO.</b></div>
                    <div class="mr5"><b>{{@root.gatePassNo}}</b></div>
                </div>
            </div>
        </div>

        <div class="flex">
            <div class="flex-grow">
                <div class="flex-start">
                    <div class="label">STOCK FROM</div>:
                    <div class="mr5">{{@root.supplierName}}</div>
                </div>
            </div>
            <div class="flex-grow">
                <div class="flex-start">
                    <div class="label-2">TO</div>:
                    <div class="mr5">{{@root.companyName}}</div>
                </div>
            </div>
        </div>

        {{else}}

        <div class='header-2'>
            <div class='row'>
                <div class='logo block'>
                    <svg width='22' height='24' viewBox='0 0 22 24' fill='none' xmlns='http://www.w3.org/2000/svg'>
                        <path d='M4.86392 9.24902H4.53815V14.3166H4.86392V9.24902Z' fill='#653332' />
                        <path fill-rule='evenodd' clip-rule='evenodd'
                            d='M13.3701 9.24902H13.7321L14.8904 14.3166H14.5465L14.2027 12.7239H12.8815L12.5376 14.3166H12.2119L13.3701 9.24902ZM13.5692 9.79197H13.533L12.9848 12.2895H14.1022L13.5692 9.79197Z'
                            fill='#653332' />
                        <path
                            d='M8.48357 14.3166H8.73694V12.4162L9.71425 9.24902H9.38848L8.61025 11.8733L7.83203 9.24902H7.52435L8.48357 12.4162V14.3166Z'
                            fill='#653332' />
                        <path d='M10.6916 9.24902H10.3658V14.3166H11.7413V13.846H10.6916V9.24902Z' fill='#653332' />
                        <path fill-rule='evenodd' clip-rule='evenodd'
                            d='M18.8721 14.3166V9.24902C18.8721 9.24902 19.4874 9.24902 19.8675 9.24902C20.0847 9.24902 20.3018 9.32924 20.6922 9.71958C21.2832 10.3106 21.2611 11.7828 21.2611 11.7828C21.2611 11.7828 21.3153 13.0135 20.7724 13.7917C20.4256 14.2887 19.7951 14.3166 19.7951 14.3166H18.8721ZM19.1254 13.846V9.75578C19.1254 9.75578 19.6865 9.75578 20.0123 9.75578C20.0666 9.75578 20.3474 9.79197 20.6922 10.3892C20.9801 10.8879 20.9715 11.8009 20.9715 11.8009C20.9715 11.8009 20.9801 12.7501 20.6922 13.2488C20.3474 13.846 19.9399 13.846 19.9399 13.846H19.1254Z'
                            fill='#653332' />
                        <path
                            d='M15.8315 9.24902H15.5781V14.3166H15.8315V9.90056L17.4604 14.3166H17.8223V9.24902H17.5327V13.6469L15.9039 9.24902H15.8315Z'
                            fill='#653332' />
                        <path
                            d='M6.49275 9.24902H6.20317H5.58783V9.75578H6.20317V14.3166H6.49275V9.75578H7.10809V9.24902H6.49275Z'
                            fill='#653332' />
                        <path
                            d='M2.29395 14.4252C1.38903 14.4252 0.737488 13.2421 0.737488 11.7828C0.737488 10.3235 1.43434 9.17664 2.29395 9.17664C2.89179 9.17664 3.41937 9.71267 3.66942 10.5521H3.30373C3.08351 10.0047 2.72062 9.611 2.29395 9.611C1.59615 9.611 1.06326 10.613 1.06326 11.7925C1.06326 12.972 1.55937 13.9546 2.29395 13.9546C2.70634 13.9546 3.05893 13.5844 3.28114 13.0678H3.65132C3.39608 13.8775 2.87881 14.4252 2.29395 14.4252Z'
                            fill='#653332' />
                        <path fill-rule='evenodd' clip-rule='evenodd'
                            d='M11.0355 0.959961C13.781 0.959961 15.4696 3.45753 16.7799 7.43917H12.7005C12.3916 4.16172 11.7953 1.6839 11.144 1.6839C10.4027 1.6839 9.33458 4.26379 8.94273 8.3079H5.16648C6.00121 3.95388 8.2899 0.959961 11.0355 0.959961ZM5.12189 15.2939C5.94324 19.7823 8.37603 23.04 11.0355 23.04C13.3241 23.04 15.6223 20.3991 16.6934 16.5969H12.667C12.2898 19.798 11.5832 21.9722 10.9812 21.9722C10.4258 21.9721 9.47657 19.1606 9.04464 15.2939H5.12189Z'
                            fill='#653332' />
                    </svg>
                </div>

                <div class='block header-title-2'>RECEIVING REPORT
                    <span class='header-company'>{{@root.companyName}}</span>
                </div>
            </div>

            <div class='page-wrapper-2'>Page
                <span><b>{{@pageData.page}}</b>
                    of
                    {{@root.totalPages}}</span>
            </div>

        </div>
        <div class='project-company-header'>

        </div>

        {{/if}}

        <div class='table-container'>
            <table>
                <thead>
                    <tr>
                        <th class="center">Item #</th>
                        <th class="center">Quantity</th>
                        <th class="center">Unit</th>
                        <th>Description</th>
                        <th>Remarks</th>
                    </tr>
                </thead>
                <tbody>
                    {{#each @pageData.data}}
                    <tr style="page-break-inside: avoid;">
                        <td class='num-col center'>{{id}}</td>
                        <td class='base-cell'>
                            {{quantity}}
                        </td>
                        <td class='unit-col center'>
                            {{unit}}
                        </td>
                        <td class='base-long-col'>
                            {{description}}
                        </td>
                        <td class='base-long-col'>
                            {{remarks}}
                        </td>
                    </tr>
                    {{/each}}

                </tbody>
            </table>
        </div>

        {{#if @last}}
        <div class="flex">
            <div class="sign-wrapper">
                <div class="sign-label"><b>RECEIVED BY:</b></div>
                <div class="sign-name">ENGR. IN-CHARGE</div>
            </div>

            <div class="sign-wrapper">
                <div class="sign-label"><b>NOTED BY:</b></div>
                <div class="sign-name">SUPERVISOR/DEPT. HEAD</div>
            </div>

            <div class="sign-wrapper">
                <div class="sign-label"><b>APPROVAL BY:</b></div>
                <div class="sign-name">PURCHASING DEPT. HEAD</div>
            </div>

            <div class="sign-wrapper">
                <div class="sign-label"><b>CLEARANCE APPROVED BY:</b></div>
                <div class="sign-name">&nbsp;</div>
            </div>
        </div>

        <div class="double-hr">
            &nbsp;
        </div>

        <div style="margin-top: 25px;">
            <b>TO BE FILLED-UP BY GUARD</b>
        </div>

        <div style="margin-top: 15px;" class="flex">
            <div>
                <div class="flex-start">
                    <div style="width: 50px;">
                        DATE
                    </div>
                    <div style="margin-left: 10px; margin-right: 10px;">:</div>
                    <div>_________________________</div>
                </div>

                <div class="flex-start">
                    <div style="width: 50px;">
                        DRIVER
                    </div>
                    <div style="margin-left: 10px; margin-right: 10px;">:</div>
                    <div>_________________________</div>
                </div>

            </div>

            <div>
                <div class="flex-start">
                    <div style="width: 100px;">
                        TIME
                    </div>
                    <div style="margin-left: 10px; margin-right: 10px;">:</div>
                    <div>_________________________</div>
                </div>

                <div class="flex-start">
                    <div style="width: 100px;">
                        VEHICLE TYPE
                    </div>
                    <div style="margin-left: 10px; margin-right: 10px;">:</div>
                    <div>_________________________</div>
                </div>

            </div>

            <div>
                <div class="flex-start">
                    <div style="width: 50px;">
                        STAFF
                    </div>
                    <div style="margin-left: 10px; margin-right: 10px;">:</div>
                    <div>_______________________</div>
                </div>

                <div class="flex-start">
                    <div style="width: 50px;">
                        PLATE#
                    </div>
                    <div style="margin-left: 10px; margin-right: 10px;">:</div>
                    <div>_______________________</div>
                </div>

            </div>

        </div>

        <div class="flex" style="margin-top: 25px;">
            <div style="flex: 50%; padding-left: 45px; padding-right: 45px">
                <div>
                    <center><b>MATERIALS RECEIVED / RELEASED BY:</b></center>
                </div>
                <div class="flex" style="gap: 20px; margin-top: 40px;">
                    <div style="flex: 50%; border-top: 1px solid black; padding-top: 5px">
                        <center>(PRINT NAME & SIGN)</center>
                    </div>
                    <div style="flex: 50%; border-top: 1px solid black; padding-top: 5px">
                        <center>DATE & TIME</center>
                    </div>
                </div>
            </div>

            <div style="flex: 50%; border: 1.8px solid black; border-radius: 10px; padding: 5px; height: 100px;">
                <b>GUARD'S REMARKS:</b>
            </div>

        </div>

        {{/if}}


    </div>
    {{/each}}



    </div>
</body>

</html>
