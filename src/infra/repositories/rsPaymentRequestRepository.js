const BaseRepository = require('./baseRepository');

class RsPaymentRequestRepository extends BaseRepository {
  constructor({ db, nonOfmItemRepository, itemRepository }) {
    super(db.rsPaymentRequestModel);
    this.db = db;
    this.Sequelize = db.Sequelize;
    this.nonOfmItemRepository = nonOfmItemRepository;
    this.itemRepository = itemRepository;
  }

  #createChargeAssociation(association, itemTypes) {
    return {
      association,
      as: association,
      required: false,
      where: {
        [this.db.Sequelize.Op.and]: [
          this.db.Sequelize.where(
            this.db.Sequelize.col('requisition.charge_to'),
            {
              [this.db.Sequelize.Op.in]: itemTypes,
            },
          ),
        ],
      },
    };
  }

  #createSupplierAssociation(association, itemTypes) {
    return {
      association,
      as: association,
      required: false,
      where: {
        [this.db.Sequelize.Op.and]: [
          this.db.Sequelize.where(
            this.db.Sequelize.col('purchaseOrder.supplier_type'),
            {
              [this.db.Sequelize.Op.in]: itemTypes,
            },
          ),
        ],
      },
    };
  }

  #transformRawQueryResult(rawResult) {
    if (!rawResult || rawResult.length === 0) {
      return null;
    }

    const firstRow = rawResult[0];
    const result = {
      id: firstRow.id,
      prNumber: firstRow.prNumber,
      purchaseOrder: null,
    };

    if (firstRow['purchaseOrder.id']) {
      result.purchaseOrder = {
        id: firstRow['purchaseOrder.id'],
        createdAt: firstRow['purchaseOrder.createdAt'],
        supplierType: firstRow['purchaseOrder.supplierType'],
        supplierId: firstRow['purchaseOrder.supplierId'],
        purchaseOrderItems: [],
        deliveryReceipts: [],
      };

      // Group by purchase order items
      const poItemsMap = new Map();
      const deliveryReceiptsMap = new Map();

      rawResult.forEach((row) => {
        // Handle Purchase Order Items
        if (row['purchaseOrderItems.id']) {
          const poItemId = row['purchaseOrderItems.id'];
          if (!poItemsMap.has(poItemId)) {
            poItemsMap.set(poItemId, {
              id: poItemId,
              canvassItemSupplier: null,
              canvassItem: null,
            });
          }

          const poItem = poItemsMap.get(poItemId);

          // Handle Canvass Item Supplier
          if (row['canvassItemSupplier.id'] && !poItem.canvassItemSupplier) {
            poItem.canvassItemSupplier = {
              id: row['canvassItemSupplier.id'],
              price: row['canvassItemSupplier.price'],
              quantity: row['canvassItemSupplier.quantity'],
              term: row['canvassItemSupplier.term'],
              supplierId: row['canvassItemSupplier.supplierId'],
              supplierType: row['canvassItemSupplier.supplierType'],
            };
          }

          // Handle Canvass Item
          if (row['canvassItem.id'] && !poItem.canvassItem) {
            poItem.canvassItem = {
              id: row['canvassItem.id'],
              status: row['canvassItem.status'],
              requisitionItem: null,
            };

            // Handle Requisition Item
            if (row['requisitionItem.id']) {
              poItem.canvassItem.requisitionItem = {
                id: row['requisitionItem.id'],
                itemType: row['requisitionItem.itemType'],
                notes: row['requisitionItem.notes'],
                quantity: row['requisitionItem.quantity'],
                itemId: row['requisitionItem.itemId'],
              };
            }
          }
        }

        // Handle Delivery Receipts
        if (row['deliveryReceipts.id']) {
          const drId = row['deliveryReceipts.id'];
          if (!deliveryReceiptsMap.has(drId)) {
            deliveryReceiptsMap.set(drId, {
              id: drId,
              status: row['deliveryReceipts.status'],
              drNumber: row['deliveryReceipts.drNumber'],
              deliveryReceiptItems: [],
            });
          }

          const deliveryReceipt = deliveryReceiptsMap.get(drId);

          // Handle Delivery Receipt Items
          if (row['deliveryReceiptItems.id']) {
            const existingItem = deliveryReceipt.deliveryReceiptItems.find(
              (item) => item.id === row['deliveryReceiptItems.id'],
            );
            if (!existingItem) {
              deliveryReceipt.deliveryReceiptItems.push({
                id: row['deliveryReceiptItems.id'],
                itemDes: row['deliveryReceiptItems.itemDes'],
                qtyOrdered: row['deliveryReceiptItems.qtyOrdered'],
                qtyDelivered: row['deliveryReceiptItems.qtyDelivered'],
                status: row['deliveryReceiptItems.status'],
              });
            }
          }
        }
      });

      result.purchaseOrder.purchaseOrderItems = Array.from(poItemsMap.values());
      result.purchaseOrder.deliveryReceipts = Array.from(
        deliveryReceiptsMap.values(),
      );
    }

    return result;
  }

  async getPaymentRequestById(paymentRequestId) {
    const paymentRequest = await this.getById(paymentRequestId, {
      include: [
        {
          association: 'requisition',
          as: 'requisition',
          include: [
            this.#createChargeAssociation('chargeToProject', ['project']),
            this.#createChargeAssociation('chargeToSupplier', ['supplier']),
            this.#createChargeAssociation('chargeToCompany', [
              'company',
              'association',
            ]),
            {
              association: 'assignee',
              as: 'assignee',
            },
          ],
        },
        {
          association: 'purchaseOrder',
          as: 'purchaseOrder',
          include: [
            this.#createSupplierAssociation('project', ['project']),
            this.#createSupplierAssociation('supplier', ['supplier']),
            this.#createSupplierAssociation('company', ['company']),
            {
              association: 'warranty',
              as: 'warranty',
            },
          ],
        },
      ],
    });

    return paymentRequest;
  }

  async getPaymentRequestsFromRequisitionId(
    requisitionId,
    { search, page, limit, sortBy },
  ) {
    const formattedOrder = [];
    const sortByObject = sortBy ? JSON.parse(sortBy) : { updatedAt: 'DESC' };
    for (const [field, direction] of Object.entries(sortByObject)) {
      if (field === 'lastApprover') {
        formattedOrder.push([
          { model: this.db.userModel, as: 'lastApprover' },
          'firstName',
          direction,
        ]);
      } else if (field === 'lastUpdate') {
        formattedOrder.push(['updatedAt', direction]);
      } else {
        formattedOrder.push([field, direction]);
      }
    }

    const requisition = await this.db.requisitionModel.findByPk(requisitionId, {
      attributes: ['companyCode'],
    });
    const companyCode = requisition.companyCode;

    const paymentRequests = await this.findAll({
      attributes: [
        'id',
        [
          this.Sequelize.fn(
            'CONCAT',
            'VR-',
            this.Sequelize.literal(
              "CASE WHEN is_draft THEN 'TMP-' ELSE '' END",
            ),
            companyCode,
            this.Sequelize.col('pr_letter'),
            this.Sequelize.literal(`
              CASE WHEN is_draft
              THEN draft_pr_number
              ELSE pr_number
              END
            `),
          ),
          'prNumber',
        ],
        'updatedAt',
        'status',
      ],
      where: {
        [this.Sequelize.Op.and]: [
          search
            ? this.Sequelize.where(
                this.Sequelize.fn(
                  'CONCAT',
                  'VR-',
                  this.Sequelize.literal(
                    "CASE WHEN is_draft THEN 'TMP-' ELSE '' END",
                  ),
                  companyCode,
                  this.Sequelize.col('pr_letter'),
                  this.Sequelize.literal(`
                  CASE WHEN is_draft
                  THEN draft_pr_number
                  ELSE pr_number
                  END
                `),
                ),
                { [this.Sequelize.Op.iLike]: `%${search}%` },
              )
            : null,
          { requisitionId },
        ],
      },
      include: [
        {
          model: this.db.userModel,
          association: 'lastApprover',
          attributes: ['id', 'firstName', 'lastName'],
        },
      ],
      paginate: true,
      page,
      limit,
      order: [
        ...formattedOrder,
        [{ model: this.db.userModel, as: 'lastApprover' }, 'firstName', 'ASC'],
      ],
    });

    return paymentRequests;
  }

  async getAllItemsInPaymentRequestByPurchaseOrder(paymentRequestId) {
    const existingPr = await this.getById(paymentRequestId);

    const requisition = await this.db.requisitionModel.findByPk(
      existingPr.requisitionId,
    );

    const rsNumber = `RS-${String(requisition.companyCode).padStart(2, '0')}${requisition.rsLetter}${requisition.rsNumber}`;

    const rawQuery = `
      SELECT
        pr.id,
        CONCAT('VR-', :companyCode, pr.pr_letter, pr.pr_number) as "prNumber",

        -- Purchase Order data
        po.id as "purchaseOrder.id",
        po.created_at as "purchaseOrder.createdAt",
        po.supplier_type as "purchaseOrder.supplierType",
        po.supplier_id as "purchaseOrder.supplierId",

        -- Purchase Order Items data
        poi.id as "purchaseOrderItems.id",

        -- Canvass Item Supplier data
        cis.id as "canvassItemSupplier.id",
        cis.unit_price as "canvassItemSupplier.price",
        cis.quantity as "canvassItemSupplier.quantity",
        cis.term as "canvassItemSupplier.term",
        cis.supplier_id as "canvassItemSupplier.supplierId",
        cis.supplier_type as "canvassItemSupplier.supplierType",

        -- Canvass Item data
        ci.id as "canvassItem.id",
        ci.status as "canvassItem.status",

        -- Requisition Item data
        ril.id as "requisitionItem.id",
        ril.item_type as "requisitionItem.itemType",
        ril.notes as "requisitionItem.notes",
        ril.quantity as "requisitionItem.quantity",
        ril.item_id as "requisitionItem.itemId",

        -- Delivery Receipt data
        dr.id as "deliveryReceipts.id",
        dr.status as "deliveryReceipts.status",
        dr.dr_number as "deliveryReceipts.drNumber",

        -- Delivery Receipt Items data
        dri.id as "deliveryReceiptItems.id",
        dri.item_des as "deliveryReceiptItems.itemDes",
        dri.qty_ordered as "deliveryReceiptItems.qtyOrdered",
        dri.qty_delivered as "deliveryReceiptItems.qtyDelivered",
        dri.status as "deliveryReceiptItems.status"

      FROM rs_payment_requests pr
      LEFT JOIN purchase_orders po ON pr.purchase_order_id = po.id AND po.was_cancelled = false
      LEFT JOIN purchase_order_items poi ON po.id = poi.purchase_order_id
      LEFT JOIN canvass_item_suppliers cis ON poi.canvass_item_supplier_id = cis.id
      LEFT JOIN canvass_items ci ON poi.canvass_item_id = ci.id
      LEFT JOIN requisition_item_lists ril ON ci.requisition_item_list_id = ril.id
      LEFT JOIN delivery_receipts dr ON po.id = dr.po_id AND dr.status = 'Delivered'
      LEFT JOIN delivery_receipt_items dri ON dr.id = dri.dr_id

      WHERE pr.id = :paymentRequestId
      ORDER BY poi.id, cis.id, ci.id, ril.id, dr.id, dri.id
    `;

    const rawResult = await this.db.sequelize.query(rawQuery, {
      replacements: {
        paymentRequestId,
        companyCode: requisition.companyCode,
      },
      type: this.db.Sequelize.QueryTypes.SELECT,
    });

    // Transform the flat result into nested structure
    const result = this.#transformRawQueryResult(rawResult);

    // Fetch item details separately to avoid join conflicts
    if (
      result &&
      result.purchaseOrder &&
      result.purchaseOrder.purchaseOrderItems
    ) {
      for (const poItem of result.purchaseOrder.purchaseOrderItems) {
        if (poItem.canvassItem && poItem.canvassItem.requisitionItem) {
          const requisitionItem = poItem.canvassItem.requisitionItem;
          const itemType = requisitionItem.itemType; // Fixed: was requisitionItem.it

          // Fetch the appropriate item details based on itemType
          if (itemType === 'non-ofm' || itemType === 'non-ofm-tom') {
            const nonOfmItem = await this.db.nonOfmItemModel.findByPk(
              requisitionItem.itemId,
              {
                attributes: ['id', 'itemName', 'itemType', 'unit', 'acctCd'],
              },
            );
            requisitionItem.nonOfmItem = nonOfmItem;
          } else if (itemType === 'ofm' || itemType === 'ofm-tom') {
            const item = await this.db.itemModel.findByPk(
              requisitionItem.itemId,
              {
                attributes: ['id', 'itemCd', 'itmDes', 'unit', 'acctCd'],
              },
            );
            requisitionItem.item = item;
          }
        }
      }
    }

    console.log('tae', result?.purchaseOrder?.purchaseOrderItems);

    return { ...result, chargeTo: requisition.chargeTo, rsNumber };
  }
}
module.exports = RsPaymentRequestRepository;
