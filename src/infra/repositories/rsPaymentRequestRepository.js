const BaseRepository = require('./baseRepository');

class RsPaymentRequestRepository extends BaseRepository {
  constructor({ db }) {
    super(db.rsPaymentRequestModel);
    this.db = db;
    this.Sequelize = db.Sequelize;
  }

  #createChargeAssociation(association, itemTypes) {
    return {
      association,
      as: association,
      required: false,
      where: {
        [this.db.Sequelize.Op.and]: [
          this.db.Sequelize.where(
            this.db.Sequelize.col('requisition.charge_to'),
            {
              [this.db.Sequelize.Op.in]: itemTypes,
            },
          ),
        ],
      },
    };
  }

  #createSupplierAssociation(association, itemTypes) {
    return {
      association,
      as: association,
      required: false,
      where: {
        [this.db.Sequelize.Op.and]: [
          this.db.Sequelize.where(
            this.db.Sequelize.col('purchaseOrder.supplier_type'),
            {
              [this.db.Sequelize.Op.in]: itemTypes,
            },
          ),
        ],
      },
    };
  }

  async getPaymentRequestById(paymentRequestId) {
    const paymentRequest = await this.getById(paymentRequestId, {
      include: [
        {
          association: 'requisition',
          as: 'requisition',
          include: [
            this.#createChargeAssociation('chargeToProject', ['project']),
            this.#createChargeAssociation('chargeToSupplier', ['supplier']),
            this.#createChargeAssociation('chargeToCompany', [
              'company',
              'association',
            ]),
            {
              association: 'assignee',
              as: 'assignee',
            },
          ],
        },
        {
          association: 'purchaseOrder',
          as: 'purchaseOrder',
          include: [
            this.#createSupplierAssociation('project', ['project']),
            this.#createSupplierAssociation('supplier', ['supplier']),
            this.#createSupplierAssociation('company', ['company']),
            {
              association: 'warranty',
              as: 'warranty',
            },
          ],
        },
      ],
    });

    return paymentRequest;
  }

  async getPaymentRequestsFromRequisitionId(
    requisitionId,
    { search, page, limit, sortBy },
  ) {
    const formattedOrder = [];
    const sortByObject = sortBy ? JSON.parse(sortBy) : { updatedAt: 'DESC' };
    for (const [field, direction] of Object.entries(sortByObject)) {
      if (field === 'lastApprover') {
        formattedOrder.push([
          { model: this.db.userModel, as: 'lastApprover' },
          'firstName',
          direction,
        ]);
      } else if (field === 'lastUpdate') {
        formattedOrder.push(['updatedAt', direction]);
      } else {
        formattedOrder.push([field, direction]);
      }
    }

    const requisition = await this.db.requisitionModel.findByPk(requisitionId, {
      attributes: ['companyCode'],
    });
    const companyCode = requisition.companyCode;

    const paymentRequests = await this.findAll({
      attributes: [
        'id',
        [
          this.Sequelize.fn(
            'CONCAT',
            'VR-',
            this.Sequelize.literal(
              "CASE WHEN is_draft THEN 'TMP-' ELSE '' END",
            ),
            companyCode,
            this.Sequelize.col('pr_letter'),
            this.Sequelize.literal(`
              CASE WHEN is_draft
              THEN draft_pr_number
              ELSE pr_number
              END
            `),
          ),
          'prNumber',
        ],
        'updatedAt',
        'status',
      ],
      where: {
        [this.Sequelize.Op.and]: [
          search
            ? this.Sequelize.where(
                this.Sequelize.fn(
                  'CONCAT',
                  'VR-',
                  this.Sequelize.literal(
                    "CASE WHEN is_draft THEN 'TMP-' ELSE '' END",
                  ),
                  companyCode,
                  this.Sequelize.col('pr_letter'),
                  this.Sequelize.literal(`
                  CASE WHEN is_draft
                  THEN draft_pr_number
                  ELSE pr_number
                  END
                `),
                ),
                { [this.Sequelize.Op.iLike]: `%${search}%` },
              )
            : null,
          { requisitionId },
        ],
      },
      include: [
        {
          model: this.db.userModel,
          association: 'lastApprover',
          attributes: ['id', 'firstName', 'lastName'],
        },
      ],
      paginate: true,
      page,
      limit,
      order: [
        ...formattedOrder,
        [{ model: this.db.userModel, as: 'lastApprover' }, 'firstName', 'ASC'],
      ],
    });

    return paymentRequests;
  }

  async getAllItemsInPaymentRequestByPurchaseOrder(paymentRequestId) {
    const existingPr = await this.getById(paymentRequestId);

    const requisition = await this.db.requisitionModel.findByPk(
      existingPr.requisitionId,
    );

    const rsNumber = `RS-${String(requisition.companyCode).padStart(2, '0')}${requisition.rsLetter}${requisition.rsNumber}`;

    const result = await this.findOne({
      where: { id: paymentRequestId },
      attributes: [
        'id',
        [
          this.Sequelize.fn(
            'CONCAT',
            'VR-',
            requisition.companyCode,
            this.Sequelize.col('pr_letter'),
            this.Sequelize.literal(`pr_number
            `),
          ),
          'prNumber',
        ],
      ],
      include: [
        {
          model: this.db.purchaseOrderModel,
          as: 'purchaseOrder',
          attributes: ['id', 'createdAt', 'supplierType', 'supplierId'],
          where: { wasCancelled: false },
          include: [
            {
              model: this.db.purchaseOrderItemModel,
              as: 'purchaseOrderItems',
              attributes: ['id'],
              include: [
                {
                  model: this.db.canvassItemSupplierModel,
                  as: 'canvassItemSupplier',
                },
                {
                  model: this.db.canvassItemModel,
                  as: 'canvassItem',
                  include: [
                    {
                      model: this.db.requisitionItemListModel,
                      as: 'requisitionItem',
                      include: [
                        {
                          model:
                            requisitionItem.it === 'non-ofm'
                              ? this.db.nonOfmItemModel
                              : this.db.itemModel,
                          as:
                            requisitionItem.it === 'non-ofm'
                              ? 'nonOfmItem'
                              : 'item',
                        },
                      ],
                    },
                  ],
                  attributes: ['id'],
                },
              ],
            },
            // {
            //   model: this.db.deliveryReceiptModel,
            //   as: 'deliveryReceipts',
            //   attributes: ['id'],
            //   where: {
            //     status: 'Delivered',
            //   },
            //   include: [
            //     {
            //       model: this.db.deliveryReceiptItemModel,
            //       as: 'deliveryReceiptItems',
            //     },
            //   ],
            // },
          ],
        },
      ],
    });

    console.log('tae', result.purchaseOrder.purchaseOrderItems);

    return { ...result, chargeTo: requisition.chargeTo, rsNumber };
  }
}
module.exports = RsPaymentRequestRepository;
