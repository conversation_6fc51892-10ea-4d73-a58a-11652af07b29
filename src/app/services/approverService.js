class ApproverService {
  constructor(container) {
    const {
      approvalTypeRepository,
      clientErrors,
      requisitionApproverRepository,
      canvassApproverRepository,
      purchaseOrderApproverRepository,
      nonRequisitionApproverRepository,
      rsPaymentRequestApproverRepository,
      notificationService,
      constants,
      db,
      fastify,
      rsPaymentRequestRepository,
      nonRequisitionRepository,
      purchaseOrderRepository,
      canvassRequisitionRepository,
      requisitionRepository,
    } = container;
    this.clientErrors = clientErrors;
    this.approvalTypeRepository = approvalTypeRepository;
    this.requisitionApproverRepository = requisitionApproverRepository;
    this.canvassApproverRepository = canvassApproverRepository;
    this.purchaseOrderApproverRepository = purchaseOrderApproverRepository;
    this.nonRequisitionApproverRepository = nonRequisitionApproverRepository;
    this.rsPaymentRequestApproverRepository =
      rsPaymentRequestApproverRepository;
    this.notificationService = notificationService;
    this.constants = constants;
    this.db = db;
    this.fastify = fastify;
    this.rsPaymentRequestRepository = rsPaymentRequestRepository;
    this.nonRequisitionRepository = nonRequisitionRepository;
    this.purchaseOrderRepository = purchaseOrderRepository;
    this.canvassRequisitionRepository = canvassRequisitionRepository;
    this.requisitionRepository = requisitionRepository;
  }

  async getLowestLevelApproverObject(approvers) {
    if (!approvers || approvers.length === 0) {
      return null;
    }

    const getEffectiveLevel = (approver) => {
      if ('approverLevel' in approver) {
        return approver.approverLevel;
      } else if ('level' in approver) {
        return approver.level;
      }

      console.warn(
        "Approver object has neither 'approver_level' nor 'level' property:",
        approver,
      );
      return Infinity;
    };

    let lowestLevelApprover = approvers[0];
    let lowestEffectiveLevel = getEffectiveLevel(approvers[0]);

    for (let i = 1; i < approvers.length; i++) {
      const currentApprover = approvers[i];
      const currentEffectiveLevel = getEffectiveLevel(currentApprover);

      if (currentEffectiveLevel < lowestEffectiveLevel) {
        lowestEffectiveLevel = currentEffectiveLevel;
        lowestLevelApprover = currentApprover;
      }
    }

    return lowestLevelApprover;
  }

  async overrideApprover(payload = {}) {
    const {
      model,
      modelId,
      approverId,
      transaction,
      status = 'approved',
      requisitionId = null,
    } = payload;

    this.fastify.log.info(`Initiating override of approvers for ${model}`);

    const { NOTIFICATION_DETAILS } = this.constants.notification;

    const order =
      model === 'requisition'
        ? [['approver_level', 'ASC']]
        : [['level', 'ASC']];

    const { APPROVER } = this.constants.approver;

    if (!(model in APPROVER.MODEL_APPROVER)) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Invalid model type provided: ${model}`,
      });
    }

    this.fastify.log.info(`Fetching of approvers for ${model}`);
    const { data: approvers } = await this[
      APPROVER.MODEL_APPROVER[model]
    ].findAll({
      where: APPROVER.WHERE_MODEL_ID_APPROVER[model](modelId),
      order,
      include: [
        {
          model: this.db.userModel,
          attributes: ['id', 'firstName', 'lastName', 'username'],
          as: 'altApprover',
          include: [
            { model: this.db.roleModel, attributes: ['name'], as: 'role' },
          ],
        },
        {
          model: this.db.userModel,
          attributes: ['id', 'firstName', 'lastName', 'username'],
          as: 'approver',
          include: [
            { model: this.db.roleModel, attributes: ['name'], as: 'role' },
          ],
        },
      ],
    });

    const currentApprovers = approvers.filter((approver) => {
      return (
        approver.altApproverId === approverId ||
        approver.userId === approverId ||
        (approver.approverId === approverId && approver.status === 'pending')
      );
    });

    const currentApprover =
      await this.getLowestLevelApproverObject(currentApprovers);

    if (!currentApprover) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Approver not found for ${model}`,
      });
    }

    // const previousLevelApprover = approvers.find(
    //   (approver) =>
    //     (model === 'requisition'
    //       ? approver.approverLevel === currentApprover?.approverLevel - 1
    //       : approver.level === currentApprover?.level - 1 ||
    //         (currentApprover.isAdhoc
    //           ? approver.approverLevel === currentApprover?.approverLevel
    //           : false) ||
    //         (currentApprover.altApprover
    //           ? approver.approverLevel === currentApprover?.approverLevel
    //           : false)) && approver.status === 'pending',
    // );

    const previousLevelApprover = approvers.find(
      (approver) =>
        approver.status === 'pending' &&
        ((model === 'requisiiton'
          ? approver.approverLevel === currentApprover?.approverLevel - 1
          : approver.level === currentApprover?.level - 1) ||
          (currentApprover.isAdhoc
            ? (approver.approverLevel === currentApprover?.approverLevel &&
                approver.userId !== currentApprover.userId) ||
              (approver.level === currentApprover?.level &&
                approver.userId !== currentApprover.userId)
            : false) ||
          (currentApprover.altApprover
            ? approver.approverLevel === currentApprover?.approverLevel
            : false)),
    );

    this.fastify.log.info(`Checking to override approvers...`);

    if (!previousLevelApprover) {
      return this.fastify.log.info(`No approvers to override.`);
    }

    if (
      previousLevelApprover?.userId
        ? previousLevelApprover?.userId === currentApprover?.userId
        : previousLevelApprover?.approverId === currentApprover?.approverId
    ) {
      return this.fastify.log.info(`Same approver no need to override`);
    }

    const overrideBy = {
      level: currentApprover.level,
      firstName:
        currentApprover.altApprover?.firstName ||
        currentApprover.approver?.firstName,
      lastName:
        currentApprover.altApprover?.lastName ||
        currentApprover.approver?.lastName,
      username:
        currentApprover.altApprover?.username ||
        currentApprover.approver?.username,
      role:
        currentApprover.altApprover?.username ||
        currentApprover.approver?.username,
      approverId,
      isAdhoc:
        currentApprover?.isAdditionalApprover || currentApprover?.isAdhoc,
    };

    this.fastify.log.info(`Updating approvers for ${model}`);

    await this[APPROVER.MODEL_APPROVER[model]].update(
      { id: previousLevelApprover.id },
      {
        status,
        overrideBy,
      },
    );

    this.fastify.log.info(
      `Sending notification for approver ${
        previousLevelApprover.altApproverId ||
        previousLevelApprover.approverId ||
        previousLevelApprover.userId
      }`,
    );

    let metaData = {
      approverId,
    };

    if (model === 'nonRequisition') {
      metaData.nonRsId = modelId;
    } else {
      metaData[`${model}Id`] = modelId;
    }

    if (model === 'canvass' && requisitionId) {
      metaData.requisitionId = requisitionId;
    }

    if (model !== 'requisition' && model !== 'nonRequisition') {
      const { requisitionId } = await this[APPROVER.MODEL[model]].findOne({
        where: { id: modelId },
      });

      metaData.requisitionId = requisitionId;
    }

    await this.notificationService.sendNotification(
      {
        senderId: approverId,

        type: APPROVER.NOTIFICATION_TYPES[model],

        title: NOTIFICATION_DETAILS.APPROVER.title(model),

        message: NOTIFICATION_DETAILS.APPROVER.message(
          APPROVER.NOTIFICATION_FEATURE[model],
        ),

        recipientUserIds: [
          previousLevelApprover.altApproverId ||
            previousLevelApprover.approverId ||
            previousLevelApprover.userId,
        ],

        metaData,
      },
      { transaction },
    );

    this.fastify.log.info(`Override approvers for ${model} success`);
  }

  async resubmitApprover(payload = {}) {
    const { model, modelId, userId } = payload;

    const { APPROVER } = this.constants.approver;
    const { NOTIFICATION_DETAILS } = this.constants.notification;

    this.fastify.log.info(`Fetching of approvers for ${model}`);
    const { data: approvers } = await this[
      APPROVER.MODEL_APPROVER[model]
    ].findAll({
      where: APPROVER.WHERE_MODEL_ID_APPROVER[model](modelId),
      order: [['level', 'ASC']],
    });

    const rejectedApprovers = approvers.filter((approver) => {
      return approver.status === 'rejected';
    });

    rejectedApprovers.forEach(async (rejectedApprover) => {
      await this[APPROVER.MODEL_APPROVER[model]].update(
        { id: rejectedApprover.id },
        { status: 'pending', overrideBy: null },
      );

      await this.notificationService.sendNotification({
        senderId: userId,

        type: APPROVER.NOTIFICATION_TYPES[model],

        title: NOTIFICATION_DETAILS.RESUBMIT_APPROVER.title(
          APPROVER.NOTIFICATION_FEATURE[model],
        ),

        message: NOTIFICATION_DETAILS.RESUBMIT_APPROVER.message(
          APPROVER.NOTIFICATION_FEATURE[model],
        ),

        recipientUserIds: [
          rejectedApprover.altApproverId ||
            rejectedApprover.approverId ||
            rejectedApprover.userId,
        ],

        metaData: {
          [`${model}Id`]: modelId,
        },
      });
    });

    let status;

    switch (model) {
      case 'requisition':
        status = 'for_rs_approval';
        break;
      default:
        status = 'for_approval';
    }

    await this[APPROVER.MODEL[model]].update(
      { id: modelId },
      {
        status,
      },
    );
  }
}

module.exports = ApproverService;
