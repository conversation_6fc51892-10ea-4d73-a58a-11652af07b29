class CompanyService {
  constructor(container) {
    const {
      utils,
      fastify,
      constants,
      clientErrors,
      syncRepository,
      companyRepository,
      requisitionRepository,
      notificationRepository,
      userRepository,
      db,
      requisitionBadgeRepository,
    } = container;

    this.utils = utils;
    this.fastify = fastify;
    this.constants = constants;
    this.clientErrors = clientErrors;
    this.syncRepository = syncRepository;
    this.companyRepository = companyRepository;
    this.httpClient = new utils.HTTPClient({
      endpoint: `${process.env.CITYLAND_API_URL}/companies`,
    });
    this.requisitionRepository = requisitionRepository;
    this.notificationRepository = notificationRepository;
    this.userRepository = userRepository;
    this.db = db;
    this.requisitionBadgeRepository = requisitionBadgeRepository;
  }

  // TODO: Update mapping to match the api response
  async syncCompanies(payload) {
    const result = await this.httpClient.get();
    const companies = result.data ?? [];
    const { userFromToken } = payload;

    const { id: userId } = userFromToken;

    const mappedCompanies = companies.map((company) => ({
      code: company.COMPCD,
      name: company.CONAME ?? '',
      initial: company.COINIT ?? '',
      tin: company.TIN ?? '',
      address: company.COMADD,
      contactNumber: company.COMNUM,
    }));

    // console.log('mappedCompanies', mappedCompanies);
    // const mappedCompanies = [
    //   {
    //     code: 1,
    //     name: 'A CITYLAND DEVELOPMENT CORPORATION',
    //     initial: 'CDC',
    //     tin: '***********-000',
    //     address: '2/F CITYLAND CONDOMINIUM 10 TOWER I H.V DELA COSTA ST BEL-AIR 1209 CITY OF MAKATI, NCR, FOURTH DISTRICT PHILIPPINES',
    //     contactNumber: '88936060'
    //   },
    //   {
    //     code: 2,
    //     name: 'B CITYLAND, INC.',
    //     initial: 'CINC',
    //     tin: '***********-000',
    //     address: '3/F CITYLAND CONDOMINIUM 10 TOWER I H.V DELA COSTA ST BEL-AIR 1209 CITY OF MAKATI, NCR, FOURTH DISTRICT PHILIPPINES',
    //     contactNumber: '88936060'
    //   }
    // ];

    const companiesWithUpdate = await this.companyWithUpdate(mappedCompanies);

    const { COMPANY_SYNC_ACCEPTED_STATUSES } = this.constants.sync;
    const { REQUISITION_STATUS } = this.constants.requisition;

    const rsOnly = await this.requisitionRepository.findAll({
      where: {
        companyId: companiesWithUpdate.map((company) => company.id),
        status: [REQUISITION_STATUS.DRAFT, REQUISITION_STATUS.SUBMITTED],
      },
      attributes: ['id', 'createdBy', 'status', 'assignedTo', 'companyId'],
      distinct: true,
      paginate: false,
    });

    const { all: rsInProgress } =
      await this.requisitionRepository.getAllRequisitionsV2({
        userFromToken,
        limit: 1_000_000,
        filterBy: {
          root_status: REQUISITION_STATUS.RS_IN_PROGRESS,
          statuses: COMPANY_SYNC_ACCEPTED_STATUSES,
          companies: companiesWithUpdate.map((company) => company.id),
        },
      });

    this.fastify.log.info(`rsInProgress: ${JSON.stringify(rsInProgress)}`);

    const toUpdate = [];

    if (rsOnly.length > 1) {
      toUpdate.push(rsOnly);
    }

    if (rsInProgress.length > 1) {
      toUpdate.push(rsInProgress);
    }

    if (toUpdate[0].length > 0) {
      this.fastify.log.info('START PROCESS');

      const { NOTIFICATION_TYPES, NOTIFICATION_DETAILS } =
        this.constants.notification;
      const canvassStatus = ['partially_canvassed', 'canvass_approval'];

      const transaction = await this.db.sequelize.transaction();

      try {
        await Promise.all(
          toUpdate[0].map(async (rs) => {
            this.fastify.log.info('START SENDING NOTIFICATION');
            await this.notificationRepository.create(
              {
                title: NOTIFICATION_DETAILS.COMPANY_SYNC.title,
                message: NOTIFICATION_DETAILS.COMPANY_SYNC.message,
                type: this.utils.getNotificationType(
                  rs.status,
                  NOTIFICATION_TYPES,
                  canvassStatus,
                ),
                recipientUserIds: await this.getNotifRecipients(rs),
                senderId: userId,
                metaData: {
                  requisitionId: rs.grouping_id,
                },
              },
              { transaction },
            );

            if (rs.doc_type !== 'Non-R.S.') {
              this.fastify.log.info('CREATING BADGE');

              await this.requisitionBadgeRepository.create({
                requisitionId: rs.grouping_id,
                createdBy: userId,
                model: 'company',
                modelId: rs.company_id,
                transaction,
              });

              this.fastify.log.info('UPDATING RS');

              // work around to put the RS with suppliers at the top of the list
              await this.requisitionRepository.updateRSForBadge({
                rsIds: [rs.grouping_id],
                transaction,
              });
              this.fastify.log.info('END SENDING NOTIFICATION');
            }
          }),
        );

        await transaction.commit();
      } catch (error) {
        this.fastify.log.error('ERROR_NOTIFYING_RS_USERS', error);
        await transaction.rollback();
        throw error;
      }
    }

    await this.companyRepository.syncCompanies(mappedCompanies);

    return await this.syncRepository.updateLastSynced('company');
  }

  async companyWithUpdate(mappedCompanies) {
    const existingCompanies = await this.companyRepository.findAll({
      where: {
        code: mappedCompanies.map((company) => company.code),
      },
      attributes: [
        'id',
        'code',
        'name',
        'initial',
        'tin',
        'address',
        'contactNumber',
      ],
      paginate: false,
    });

    this.fastify.log.info(
      `existingCompanies: ${JSON.stringify(existingCompanies.data)}`,
    );
    this.fastify.log.info(
      `mappedCompanies: ${JSON.stringify(mappedCompanies)}`,
    );

    const companiesWithUpdate = existingCompanies.data.filter(
      (existingCompany) => {
        const mappedCompany = mappedCompanies.find(
          (company) => company.code === existingCompany.code,
        );
        if (mappedCompany) {
          const isMatch =
            existingCompany.code === mappedCompany.code &&
            existingCompany.name === mappedCompany.name &&
            existingCompany.initial === mappedCompany.initial &&
            existingCompany.tin === mappedCompany.tin &&
            existingCompany.address === mappedCompany.address &&
            existingCompany.contactNumber === mappedCompany.contactNumber;

          return !isMatch;
        }

        return true;
      },
    );

    this.fastify.log.info(
      `companiesWithUpdate: ${JSON.stringify(companiesWithUpdate)}`,
    );

    return companiesWithUpdate;
  }

  // no notification service for the comapny

  async getNotifRecipients(rsDetails) {
    const { USER_TYPES } = this.constants.user;
    const { REQUISITION_STATUS } = this.constants.requisition;
    const recipients = [];
    const { assigned_to_user_id, requestor_id } = rsDetails;

    const purchasingHeadId = await this.userRepository.findOne({
      attributes: ['id'],
      include: [
        {
          model: this.db.roleModel,
          as: 'role',
          where: {
            name: USER_TYPES.PURCHASING_HEAD,
          },
        },
      ],
    });

    if (assigned_to_user_id) {
      recipients.push(assigned_to_user_id);
    }

    if (requestor_id) {
      recipients.push(requestor_id);
    }

    if (
      purchasingHeadId &&
      rsDetails.root_status === REQUISITION_STATUS.RS_IN_PROGRESS
    ) {
      recipients.push(purchasingHeadId.id);
    }

    this.fastify.log.info(`recipients: ${JSON.stringify(recipients)}`);

    return recipients;
  }
}

module.exports = CompanyService;
