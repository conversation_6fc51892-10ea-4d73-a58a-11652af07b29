const puppeteer = require('puppeteer-core');
const path = require('path');
const fs = require('fs/promises');
const { PDFDocument, rgb, StandardFonts } = require('pdf-lib');

class TemplateService {
  constructor(container) {
    const {
      db,
      utils,
      fastify,
      handlebars,
      clientErrors,
      puppeteerBrowser,
      requisitionRepository,
      companyRepository,
      requisitionItemListRepository,
      rsPaymentRequestService,
    } = container;
    this.db = db;
    this.utils = utils;
    this.fastify = fastify;
    this.clientErrors = clientErrors;
    this.requisitionRepository = requisitionRepository;
    this.requisitionItemListRepository = requisitionItemListRepository;
    this.rsPaymentRequestService = rsPaymentRequestService;
    this.companyRepository = companyRepository;
    this.handlebars = handlebars;
    this.browser = puppeteerBrowser;
    this.handlebarsTemplateFolder = path.join(
      path.resolve(),
      'src',
      'templates',
      'hbs',
    );
  }

  async generateDynamicTemplate(
    data,
    templateName,
    outputPath,
    baseName = 'template',
    additionalOptions = {},
  ) {
    const templatePath = path.join(this.handlebarsTemplateFolder, templateName);
    const templateStats = await fs.stat(templatePath);

    let templateFile;
    let headerTemplate;
    let footerTemplate;

    if (templateStats.isDirectory()) {
      templateFile = await this.utils.readFile(
        path.join(templatePath, 'index.hbs'),
        'utf-8',
        false,
      );
      headerTemplate = await this.utils.readFile(
        path.join(templatePath, 'header.hbs'),
        'utf-8',
        true,
      );

      footerTemplate = await this.utils.readFile(
        path.join(templatePath, 'footer.hbs'),
        'utf-8',
        true,
      );
    } else {
      templateFile = await this.utils.readFile(
        path.join(templatePath),
        'utf-8',
        false,
      );
    }

    const compiledTemplate = this.handlebars.compile(templateFile);
    const html = compiledTemplate(data);

    this.fastify.log.info('Starting new page...');
    const page = await this.browser.newPage();
    await page.setContent(html, { waitUntil: 'networkidle0' });

    const pdfOptions = {
      preferCSSPageSize: true,
      displayHeaderFooter: true,
      waitForFonts: true,
      headerTemplate: '<div></div>',
      footerTemplate: '<div></div>',
      ...additionalOptions,
    };

    if (headerTemplate) {
      const compiledTemplate = this.handlebars.compile(headerTemplate);
      const compiledHeaderTemplate = compiledTemplate(data);

      pdfOptions.headerTemplate = compiledHeaderTemplate;
    }
    if (footerTemplate) {
      const compiledTemplate = this.handlebars.compile(footerTemplate);
      const compiledFooterTemplate = compiledTemplate(data);

      pdfOptions.footerTemplate = compiledFooterTemplate;
    }

    this.fastify.log.info('Generating PDF...');
    const pdfBytes = await page.pdf(pdfOptions);

    await page.close(); // Close page but keep browser open

    this.fastify.log.info('Saving PDF...');

    const dateNow = new Date();
    const currentDate = this.utils.formatDateYYYYMMDD(dateNow);
    const currentTime = this.utils.convertToHHMMSS(dateNow);
    const fileName = `${baseName}-${currentDate}-${currentTime}.pdf`;

    const outputDir = path.join(path.resolve(), 'upload', outputPath);
    await fs.mkdir(outputDir, { recursive: true });

    const uploadPath = path.join(outputDir, fileName);
    await fs.writeFile(uploadPath, pdfBytes);

    return {
      pdfBytes,
      fileName,
      filePath: uploadPath,
    };
  }

  async generateTemplate(templates, HTML_FOLDER, PDF_FOLDER) {
    this.fastify.log.info(`HTML templates ${templates}`);

    for (const template of templates) {
      const htmlPath = path.join(HTML_FOLDER, template);
      const outputPath = path.join(
        PDF_FOLDER,
        template.replace('.html', '.pdf'),
      );

      const page = await this.browser.newPage();
      await page.goto(`file://${htmlPath}`, { waitUntil: 'networkidle0' });

      this.fastify.log.info(`Adjusting the dimensions...`);
      const { width, height } = await page.evaluate(() => {
        const body = document.body;
        const html = document.documentElement;
        return {
          width: Math.max(body.scrollWidth, html.scrollWidth),
          height: Math.max(body.scrollHeight, html.scrollHeight),
        };
      });

      await page.setViewport({ width, height, deviceScaleFactor: 3 });
      const screenshotBuffer = await page.screenshot({ fullPage: true });

      const formElements = await page.evaluate(() => {
        const elements = [];
        document.querySelectorAll('input, textarea, select').forEach((el) => {
          const rect = el.getBoundingClientRect();
          elements.push({
            type: el.tagName.toLowerCase(),
            name: el.name || el.id || '',
            x: rect.left,
            y: rect.top,
            width: rect.width,
            height: rect.height,
          });
        });
        return elements;
      });

      await page.close();

      this.fastify.log.info(`Creating the PDF templates...`);
      const pdfDoc = await PDFDocument.create();
      const image = await pdfDoc.embedPng(screenshotBuffer);
      const pdfPage = pdfDoc.addPage([width, height]);
      pdfPage.drawImage(image, { x: 0, y: 0, width, height });

      const form = pdfDoc.getForm();

      this.fastify.log.info(`Creating fillable PDF`);
      formElements.forEach((el) => {
        const fieldY = height - el.y - el.height;

        if (el.name && (el.type === 'input' || el.type === 'textarea')) {
          const field = form.createTextField(el.name);
          field.setText('');
          field.addToPage(pdfPage, {
            x: el.x,
            y: fieldY,
            width: el.width,
            height: el.height,
            borderColor: rgb(0, 0, 0),
            borderWidth: 0,
          });
        } else if (el.name && el.type === 'select') {
          form.createDropdown(el.name).addToPage(pdfPage, {
            x: el.x,
            y: fieldY,
            width: el.width,
            height: el.height,
          });
        }
      });

      const pdfBytes = await pdfDoc.save();
      this.fastify.log.info(`Saving PDF`);
      await fs.writeFile(outputPath, pdfBytes);
    }

    return this.fastify.log.info(`PDF template saved`);
  }

  async requisitionPDF(requisitionId, PDF_FOLDER) {
    try {
      const ITEMS_PER_PAGE1 = 11;
      const ITEMS_PER_PAGE2 = 12;
      const ITEM_FONT_SIZE = 8;

      const page1Path = path.join(PDF_FOLDER, 'requisition-slip-page1.pdf');
      const page2Path = path.join(PDF_FOLDER, 'requisition-slip-page2.pdf');

      const existingRequisition = await this.requisitionRepository.findOne({
        where: { id: requisitionId },
        include: [
          { model: this.db.companyModel, as: 'company' },
          { model: this.db.projectModel, as: 'project' },
          { model: this.db.departmentModel, as: 'department' },
        ],
      });

      if (!existingRequisition) {
        throw this.clientErrors.NOT_FOUND({
          message: `Requisition with ID ${requisitionId} does not exist`,
        });
      }

      const itemsResult =
        await this.requisitionItemListRepository.getAllRequisitionItems({
          requisitionId,
          type: existingRequisition.type,
          paginate: false,
        });

      const items = itemsResult.data || [];

      if (!items.length) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Requisition with ID ${requisitionId} has no items.`,
        });
      }

      const rsNumber = `RS-${String(existingRequisition.company.id).padStart(2, '0')}${existingRequisition.rsLetter}${existingRequisition.rsNumber}`;
      const finalPdf = await PDFDocument.create();

      const totalPages =
        items.length <= ITEMS_PER_PAGE1
          ? 1
          : 1 + Math.ceil((items.length - ITEMS_PER_PAGE1) / ITEMS_PER_PAGE2);

      const page1PdfBytes = await fs.readFile(page1Path);
      const page1Doc = await PDFDocument.load(page1PdfBytes);
      const page1Form = page1Doc.getForm();
      const boldFont = await page1Doc.embedFont(StandardFonts.HelveticaBold);
      const regularFont = await page1Doc.embedFont(StandardFonts.Helvetica);

      const currentPageField = page1Form.getTextField('currentPage');
      currentPageField.setText('1');
      currentPageField.setFontSize(14);
      currentPageField.defaultUpdateAppearances(boldFont);

      const totalPageField = page1Form.getTextField('totalPage');
      totalPageField.setText(totalPages.toString());
      totalPageField.setFontSize(14);
      totalPageField.defaultUpdateAppearances(boldFont);

      const rsNumberField = page1Form.getTextField('rsNumber');
      rsNumberField.setText(rsNumber);
      rsNumberField.setFontSize(12);
      rsNumberField.defaultUpdateAppearances(boldFont);

      const statusField = page1Form.getTextField('status');
      statusField.setText(
        existingRequisition.status.toUpperCase().replace(/_/g, ' '),
      );
      statusField.enableMultiline();
      statusField.setFontSize(12);
      statusField.setAlignment(1);
      statusField.defaultUpdateAppearances(boldFont);

      page1Form
        .getTextField('company')
        .setText(existingRequisition.company.name);
      page1Form.getTextField('total').setText(String(items.length));
      page1Form
        .getTextField('project')
        .setText(existingRequisition.project.name);
      page1Form
        .getTextField('department')
        .setText(existingRequisition.department.name);
      page1Form.getTextField('purpose').setText(existingRequisition.purpose);

      page1Form.getTextField('firstItemCount').setText('1');
      page1Form.getTextField('firstItemCount').setFontSize(12);
      page1Form
        .getTextField('firstItemCount')
        .defaultUpdateAppearances(boldFont);

      page1Form
        .getTextField('lastItemCount')
        .setText(Math.min(ITEMS_PER_PAGE1, items.length).toString());
      page1Form.getTextField('lastItemCount').setFontSize(12);
      page1Form
        .getTextField('lastItemCount')
        .defaultUpdateAppearances(boldFont);

      const createCenteredAppearance = (field, text, font, fontSize) => {
        field.setText(text);
        field.setFontSize(fontSize);
        field.setAlignment(1);
        field.defaultUpdateAppearances(font);
        return field;
      };

      const page1Items = items.slice(0, ITEMS_PER_PAGE1);
      page1Items.forEach((item, index) => {
        const itemNum = index + 1;

        const itemNumField = page1Form.getTextField(`itemNum${itemNum}`);
        createCenteredAppearance(
          itemNumField,
          itemNum.toString(),
          regularFont,
          ITEM_FONT_SIZE,
        );

        let itemName = '';
        if (item.itemType === 'ofm' || item.itemType === 'ofm-tom') {
          itemName = item.item?.itmDes || '';
        } else {
          itemName = item.nonOfmItem?.itemName || '';
        }

        const itemNameField = page1Form.getTextField(`itemName${itemNum}`);
        itemNameField.setText(itemName);
        itemNameField.setFontSize(ITEM_FONT_SIZE);
        itemNameField.enableMultiline();
        itemNameField.defaultUpdateAppearances(regularFont);

        let unit = '';
        if (item.itemType === 'ofm' || item.itemType === 'ofm-tom') {
          unit = item.item?.unit || '';
        } else {
          unit = item.nonOfmItem?.unit || '';
        }

        const unitField = page1Form.getTextField(`unit${itemNum}`);
        createCenteredAppearance(unitField, unit, regularFont, ITEM_FONT_SIZE);

        const remainingGfqField = page1Form.getTextField(
          `remainingGfq${itemNum}`,
        );
        createCenteredAppearance(
          remainingGfqField,
          item.itemType === 'ofm' || item.itemType === 'ofm-tom'
            ? item.item?.remainingGfq?.toString() || ''
            : '',
          regularFont,
          ITEM_FONT_SIZE,
        );

        const qtyField = page1Form.getTextField(`qty${itemNum}`);
        createCenteredAppearance(
          qtyField,
          item.quantity?.toString() || '',
          regularFont,
          ITEM_FONT_SIZE,
        );
      });

      page1Form.flatten();

      const page1Bytes = await page1Doc.save();
      const page1ForFinal = await PDFDocument.load(page1Bytes);
      const [page1] = await finalPdf.copyPages(page1ForFinal, [0]);
      finalPdf.addPage(page1);

      if (items.length > ITEMS_PER_PAGE1) {
        const remainingItems = items.length - ITEMS_PER_PAGE1;
        const additionalPagesNeeded = Math.ceil(
          remainingItems / ITEMS_PER_PAGE2,
        );

        this.fastify.log.info(
          `Need ${additionalPagesNeeded} additional pages for ${remainingItems} remaining items`,
        );

        for (
          let pageIndex = 0;
          pageIndex < additionalPagesNeeded;
          pageIndex++
        ) {
          const page2PdfBytes = await fs.readFile(page2Path);
          const page2Doc = await PDFDocument.load(page2PdfBytes);
          const page2Form = page2Doc.getForm();
          const page2BoldFont = await page2Doc.embedFont(
            StandardFonts.HelveticaBold,
          );
          const page2RegularFont = await page2Doc.embedFont(
            StandardFonts.Helvetica,
          );

          const rsNumberField = page2Form.getTextField('rsNumber');
          rsNumberField.setText(rsNumber);
          rsNumberField.setFontSize(12);
          rsNumberField.defaultUpdateAppearances(page2BoldFont);

          const currentPage = pageIndex + 2;
          const currentPageField = page2Form.getTextField('currentPage');
          currentPageField.setText(currentPage.toString());
          currentPageField.setFontSize(14);
          currentPageField.defaultUpdateAppearances(page2BoldFont);

          const totalPageField = page2Form.getTextField('totalPage');
          totalPageField.setText(totalPages.toString());
          totalPageField.setFontSize(14);
          totalPageField.defaultUpdateAppearances(page2BoldFont);

          const startItemIndex = ITEMS_PER_PAGE1 + pageIndex * ITEMS_PER_PAGE2;
          const endItemIndex = Math.min(
            startItemIndex + ITEMS_PER_PAGE2,
            items.length,
          );

          const firstItemCountField = page2Form.getTextField('firstItemCount');
          firstItemCountField.setText((startItemIndex + 1).toString());
          firstItemCountField.setFontSize(12);
          firstItemCountField.defaultUpdateAppearances(page2BoldFont);

          const lastItemCountField = page2Form.getTextField('lastItemCount');
          lastItemCountField.setText(endItemIndex.toString());
          lastItemCountField.setFontSize(12);
          lastItemCountField.defaultUpdateAppearances(page2BoldFont);

          const totalField = page2Form.getTextField('total');
          totalField.setText(String(items.length));
          totalField.setFontSize(12);
          totalField.defaultUpdateAppearances(page2BoldFont);

          const pageItems = items.slice(startItemIndex, endItemIndex);

          pageItems.forEach((item, index) => {
            const itemNum = startItemIndex + index + 1;
            const fieldIndex = index + 1;

            if (fieldIndex <= ITEMS_PER_PAGE2) {
              const itemNumField = page2Form.getTextField(
                `itemNum${fieldIndex}`,
              );
              createCenteredAppearance(
                itemNumField,
                itemNum.toString(),
                page2RegularFont,
                ITEM_FONT_SIZE,
              );

              let itemName = '';
              if (item.itemType === 'ofm' || item.itemType === 'ofm-tom') {
                itemName = item.item?.itmDes || '';
              } else {
                itemName = item.nonOfmItem?.itemName || '';
              }

              const itemNameField = page2Form.getTextField(
                `itemName${fieldIndex}`,
              );
              itemNameField.setText(itemName);
              itemNameField.setFontSize(ITEM_FONT_SIZE);
              itemNameField.enableMultiline();
              itemNameField.defaultUpdateAppearances(page2RegularFont);

              let unit = '';
              if (item.itemType === 'ofm' || item.itemType === 'ofm-tom') {
                unit = item.item?.unit || '';
              } else {
                unit = item.nonOfmItem?.unit || '';
              }

              const unitField = page2Form.getTextField(`unit${fieldIndex}`);
              createCenteredAppearance(
                unitField,
                unit,
                page2RegularFont,
                ITEM_FONT_SIZE,
              );

              const remainingGfqField = page2Form.getTextField(
                `remainingGfq${fieldIndex}`,
              );
              createCenteredAppearance(
                remainingGfqField,
                item.itemType === 'ofm' || item.itemType === 'ofm-tom'
                  ? item.item?.remainingGfq?.toString() || ''
                  : '',
                page2RegularFont,
                ITEM_FONT_SIZE,
              );

              const qtyField = page2Form.getTextField(`qty${fieldIndex}`);
              createCenteredAppearance(
                qtyField,
                item.quantity?.toString() || '',
                page2RegularFont,
                ITEM_FONT_SIZE,
              );
            }
          });

          page2Form.flatten();

          const page2Bytes = await page2Doc.save();
          const page2ForFinal = await PDFDocument.load(page2Bytes);
          const [page2] = await finalPdf.copyPages(page2ForFinal, [0]);
          finalPdf.addPage(page2);
        }
      }

      const pdfBytes = await finalPdf.save();

      const now = new Date();
      const phTime = new Date(now.getTime() + 8 * 60 * 60 * 1000);
      const year = phTime.getUTCFullYear();
      const month = String(phTime.getUTCMonth() + 1).padStart(2, '0');
      const day = String(phTime.getUTCDate()).padStart(2, '0');
      const hours = String(phTime.getUTCHours()).padStart(2, '0');
      const minutes = String(phTime.getUTCMinutes()).padStart(2, '0');
      const seconds = String(phTime.getUTCSeconds()).padStart(2, '0');

      const dateTimeString = `${year}${month}${day}-${hours}${minutes}${seconds}`;
      const fileName = `RS-${dateTimeString}.pdf`;

      return {
        pdfBytes,
        fileName,
      };
    } catch (error) {
      this.fastify.log.error(`Error generating PDF: ${error.message}`);
      throw error;
    }
  }

  async createDashboardPdf(payload) {
    const { limit = 100000, requestType = 'all', userId, ...options } = payload;
    const documents = await this.requisitionRepository.getAllRequisitionsV2({
      limit,
      userId,
      ...options,
    });

    const items = documents[`${requestType ? requestType : 'all'}`] || [];

    if (!items.length) {
      throw this.clientErrors.BAD_REQUEST({
        message: `No items`,
      });
    }

    const allItems = items.map((item, index) => ({
      refNumber: item.ref_number,
      docType: item.doc_type,
      requestor: item.requestor_name,
      company: item?.company_name || '',
      projDept: item?.project_name || '',
      lastUpdate: this.utils.convertDateToDDMMMYYYY(item.updated_at),
      status: item.status,
    }));

    const naming = {
      my_request: 'My Request',
      my_approval: 'My Approval',
      all: 'All',
    };

    const headers = {
      dashboardText: `${naming[`${requestType}`]}`,
    };

    const pageSize = 22;
    const pages = this.utils
      .paginateItems(allItems, pageSize)
      .map((items, index) => ({
        currentPage: index + 1,
        items,
        itemsCount: index * pageSize + 1,
        currentTotalItemsCount: Math.min(
          (index + 1) * pageSize,
          allItems.length,
        ),
      }));

    const data = {
      ...headers,
      pages,
      totalPage: pages.length,
      totalRows: allItems.length,
    };

    const result = await this.generateDynamicTemplate(
      data,
      'dashboard.hbs',
      'dashboard_downloads',
      'Dashboard',
    );

    return result;
  }

  async createRequisitionPdf(requisitionId) {
    const existingRequisition = await this.requisitionRepository.findOne({
      where: { id: requisitionId },
      include: [
        { model: this.db.companyModel, as: 'company' },
        { model: this.db.projectModel, as: 'project' },
        { model: this.db.departmentModel, as: 'department' },
      ],
    });

    if (!existingRequisition) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition with ID ${requisitionId} does not exist`,
      });
    }

    const isOFM = ['ofm', 'ofm-tom'].includes(existingRequisition?.type);
    const rsNumber = `RS-${String(existingRequisition.company.id).padStart(2, '0')}${existingRequisition.rsLetter}${existingRequisition.rsNumber}`;

    const itemsResult =
      await this.requisitionItemListRepository.getAllRequisitionItems({
        requisitionId,
        type: existingRequisition.type,
        paginate: false,
      });

    const items = itemsResult.data || [];

    if (!items.length) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Requisition with ID ${requisitionId} has no items.`,
      });
    }

    const allItems = items.map((item, index) => ({
      itemNum: index + 1,
      itemName: isOFM
        ? item.item?.itmDes || ''
        : item.nonOfmItem?.itemName || '',
      unit: isOFM ? item.item?.unit || '' : item.nonOfmItem?.unit || '',
      remainingGfq: isOFM ? item.item?.remainingGfq?.toString() || '' : '',
      qty: isOFM ? item.quantity || '' : item.quantity || '',
    }));

    const headers = {
      rsNumber,
      status: existingRequisition.status.toUpperCase().replace(/_/g, ' '),
      company: existingRequisition.company.name,
      department: existingRequisition.department.name,
      project: existingRequisition.project.name,
      purpose: existingRequisition.purpose,
    };

    const pageSize = 11;
    const pages = this.utils
      .paginateItems(allItems, pageSize)
      .map((items, index) => ({
        currentPage: index + 1,
        items,
        itemsCount: index * pageSize + 1,
        currentTotalItemsCount: Math.min(
          (index + 1) * pageSize,
          allItems.length,
        ),
      }));

    const data = {
      ...headers,
      items: allItems,
      pages,
      firstItemCount: 1,
      currentPage: 1,
      totalPage: pages.length,
      totalItems: allItems.length,
      lastItemCount: allItems.length,
    };

    const result = await this.generateDynamicTemplate(
      data,
      'rs-slip.hbs',
      'requisition_slip_downloads',
      'RS',
    );

    return result;
  }

  #transformPaymentRequestForTemplate(paymentRequest) {
    const items = [];
    let totalAmount = 0;

    if (paymentRequest.purchaseOrder?.purchaseOrderItems) {
      paymentRequest.purchaseOrder.purchaseOrderItems.forEach(
        (poItem, index) => {
          const canvassItem = poItem.canvassItem;
          const requisitionItem = canvassItem?.requisitionItem;
          const canvassItemSupplier = poItem.canvassItemSupplier;

          // Get item details (either OFM or non-OFM)
          const itemDetails =
            requisitionItem?.item || requisitionItem?.nonOfmItem;
          const itemName =
            itemDetails?.itmDes || itemDetails?.itemName || 'Unknown Item';
          const itemCode = itemDetails?.itemCd || 'N/A';
          const unit = itemDetails?.unit || 'pcs';

          // Calculate amounts
          const quantity = parseFloat(canvassItemSupplier?.quantity || 0);
          const unitPrice = parseFloat(canvassItemSupplier?.price || 0);
          const amount = quantity * unitPrice;
          totalAmount += amount;

          // Find related delivery receipts for this item
          const deliveryNumbers = [];
          const invoiceNumbers = [];

          if (paymentRequest.purchaseOrder.deliveryReceipts) {
            paymentRequest.purchaseOrder.deliveryReceipts.forEach((dr) => {
              if (dr.deliveryReceiptItems) {
                dr.deliveryReceiptItems.forEach((drItem) => {
                  if (drItem.itemId === requisitionItem?.itemId) {
                    if (dr.drNumber) deliveryNumbers.push(dr.drNumber);
                  }
                });
              }
            });
          }

          items.push({
            itemNumber: index + 1,
            itemCode,
            itemName,
            description: itemName,
            unit,
            quantity: quantity.toFixed(2),
            unitPrice: unitPrice.toFixed(2),
            amount: amount.toFixed(2),
            rsNumbers: paymentRequest.rsNumber,
            deliveryNumbers: deliveryNumbers.join(', '),
            invoiceNumbers: invoiceNumbers.join(', '),
            itemType: requisitionItem?.itemType || 'unknown',
            notes: requisitionItem?.notes || '',
          });
        },
      );
    }

    // Format date
    const currentDate = new Date();
    const formattedDate = currentDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    return {
      // Header Information
      prNumber: paymentRequest.prNumber,
      rsNumber: paymentRequest.rsNumber,
      date: formattedDate,

      // Vendor Information
      vendorName: paymentRequest.supplier?.name || 'N/A',
      vendorTin: paymentRequest.supplier?.tin || 'N/A',
      vendorType: paymentRequest.purchaseOrder?.supplierType || 'N/A',

      // Project/Charge Information
      chargeTo: paymentRequest.chargeTo,

      // Items
      items,

      // Financial Summary
      totalAmount: totalAmount.toFixed(2),
      totalAmountInWords: this.#numberToWords(totalAmount),
      itemCount: items.length,

      // Additional Information
      createdAt: paymentRequest.purchaseOrder?.createdAt,
      deliveryReceiptCount:
        paymentRequest.purchaseOrder?.deliveryReceipts?.length || 0,
    };
  }

  #numberToWords(amount) {
    // Simple number to words conversion for amounts
    // You can implement a more sophisticated version if needed
    const ones = [
      '',
      'One',
      'Two',
      'Three',
      'Four',
      'Five',
      'Six',
      'Seven',
      'Eight',
      'Nine',
    ];
    const teens = [
      'Ten',
      'Eleven',
      'Twelve',
      'Thirteen',
      'Fourteen',
      'Fifteen',
      'Sixteen',
      'Seventeen',
      'Eighteen',
      'Nineteen',
    ];
    const tens = [
      '',
      '',
      'Twenty',
      'Thirty',
      'Forty',
      'Fifty',
      'Sixty',
      'Seventy',
      'Eighty',
      'Ninety',
    ];
    const thousands = ['', 'Thousand', 'Million', 'Billion'];

    if (amount === 0) return 'Zero Pesos';

    const [wholePart, decimalPart] = amount.toFixed(2).split('.');
    const wholeNumber = parseInt(wholePart);
    const cents = parseInt(decimalPart);

    let result = this.#convertToWords(
      wholeNumber,
      ones,
      teens,
      tens,
      thousands,
    );
    result += ' Pesos';

    if (cents > 0) {
      result +=
        ' and ' +
        this.#convertToWords(cents, ones, teens, tens, thousands) +
        ' Centavos';
    }

    return result;
  }

  #convertToWords(num, ones, teens, tens, thousands) {
    if (num === 0) return '';

    let result = '';
    let thousandIndex = 0;

    while (num > 0) {
      const chunk = num % 1000;
      if (chunk !== 0) {
        let chunkWords = '';

        const hundreds = Math.floor(chunk / 100);
        const remainder = chunk % 100;

        if (hundreds > 0) {
          chunkWords += ones[hundreds] + ' Hundred ';
        }

        if (remainder >= 10 && remainder < 20) {
          chunkWords += teens[remainder - 10] + ' ';
        } else {
          const tensDigit = Math.floor(remainder / 10);
          const onesDigit = remainder % 10;

          if (tensDigit > 0) {
            chunkWords += tens[tensDigit] + ' ';
          }

          if (onesDigit > 0) {
            chunkWords += ones[onesDigit] + ' ';
          }
        }

        if (thousands[thousandIndex]) {
          chunkWords += thousands[thousandIndex] + ' ';
        }

        result = chunkWords + result;
      }

      num = Math.floor(num / 1000);
      thousandIndex++;
    }

    return result.trim();
  }

  async createRsPaymentRequestPdf(paymentRequestId) {
    const rawPaymentRequest =
      await this.rsPaymentRequestService.getAllItemsInPaymentRequestByPurchaseOrder(
        paymentRequestId,
      );

    // Transform data for template
    const templateData =
      this.#transformPaymentRequestForTemplate(rawPaymentRequest);

    // Generate PDF using the payment request template
    const result = await this.generateDynamicTemplate(
      templateData,
      'payment-request-voucher.hbs',
      'payment_request_downloads',
      'VR',
    );

    return result;
  }
}

module.exports = TemplateService;
