const { PDFDocument, StandardFonts } = require('pdf-lib');
const path = require('path');
const fs = require('fs');

class GatePassService {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      clientErrors,
      gatePassRepository,
      fastify,
      attachmentRepository,
      templateService,
    } = container;

    this.db = db;
    this.utils = utils;
    this.clientErrors = clientErrors;
    this.entities = entities;
    this.fastify = fastify;
    this.Sequelize = db.Sequelize;
    this.gatePassRepository = gatePassRepository;
    this.attachmentRepository = attachmentRepository;
    this.templateService = templateService;
  }

  async createAttachment(request) {
    const { userFromToken, requisitionId, body } = request;
    this.fastify.log.info(
      `Adding Gate Pass to requisition slip ${requisitionId}`,
    );

    const path = `/GatePass/${body.path}`;

    try {
      this.fastify.log.info(`Adding Gate Pass to requisition slip...`);
      return await this.attachmentRepository.create({
        model: 'requisition',
        modelId: requisitionId,
        userId: userFromToken.id,
        path,
        fileName: body.name,
      });
    } catch (error) {
      this.fastify.log.info(`Failed Adding Gate Pass to requisition slip`);
      throw error;
    }
  }

  async fillTemplate(templateBytes, items, gatePassNumber, purchaseOrder) {
    const resultDoc = await PDFDocument.create();
    const pdfDoc = await PDFDocument.load(templateBytes);
    const font = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    const today = new Date();
    const form = pdfDoc.getForm();
    const itemsPerPage = 6;
    const totalPages = Math.ceil(items.length / itemsPerPage);

    const gatePassField = form.getTextField('gatePass');
    gatePassField.setText(String(gatePassNumber).padStart(5, '0'));
    gatePassField.updateAppearances(font);

    let globalCounter = 1; // Continuous numbering across pages

    const todayString = today.toLocaleDateString('en-PH', {
      year: 'numeric',
      month: 'long',
      day: '2-digit',
    });

    for (let pageIndex = 0; pageIndex < totalPages; pageIndex++) {
      let page;

      const date = form.getTextField('date');
      date.setFontSize(10);
      date.setText(todayString);

      const supplierName = `${purchaseOrder?.purchaseOrder?.supplier?.name || purchaseOrder?.purchaseOrder?.supplierName || ''}`;
      const supplierTextField = form.getTextField('supplier');
      supplierTextField.setFontSize(supplierName.length > 35 ? 7 : 9);
      supplierTextField.setText(supplierName);

      const toTextField = form.getTextField('to');
      toTextField.setFontSize(9);
      toTextField.setText(`${purchaseOrder?.requisition?.company?.name || ''}`);

      const pageItems = items.slice(
        pageIndex * itemsPerPage,
        (pageIndex + 1) * itemsPerPage,
      );

      for (let index = 1; index <= 6; index++) {
        form.getTextField(`item${index}`).setText('');
        form.getTextField(`quantity${index}`).setText('');
        form.getTextField(`unit${index}`).setText('');
        form.getTextField(`description${index}`).setText('');
        form.getTextField(`remarks${index}`).setText('');
      }

      pageItems.forEach((item, index) => {
        let counter = globalCounter++;

        const itemNo = form.getTextField(`item${index + 1}`);
        itemNo.setFontSize(9);
        itemNo.setText(`${counter}`);

        const qty = form.getTextField(`quantity${index + 1}`);
        qty.setFontSize(9);
        qty.setText(`${item.quantityPurchased}`);

        const unit = form.getTextField(`unit${index + 1}`);
        unit.setFontSize(9);
        unit.setText(`${item.unit}`);

        const desc = form.getTextField(`description${index + 1}`);
        desc.setFontSize(9);
        desc.setText(item.itemName);

        const remarks = form.getTextField(`remarks${index + 1}`);
        desc.setFontSize(9);
        remarks.setText(item.notes);
      });

      const pages = pdfDoc.getPages();
      const firstPage = pages[0];
      const { width, height } = firstPage.getSize();
      firstPage.drawText(String(gatePassNumber).padStart(5, '0'), {
        x: width / 2 + 235,
        y: height / 2 + 266,
        size: 14,
        font: font,
      });

      templateBytes = await pdfDoc.save();
      const newPage = await PDFDocument.load(templateBytes);
      const [pageToAdd] = await resultDoc.copyPages(newPage, [0]);

      page = resultDoc.addPage(pageToAdd);
    }

    //form.flatten();

    const pdfBytes = await resultDoc.save({ useObjectStreams: false });

    return pdfBytes;
  }

  async generateGatePass(request) {
    const { userFromToken, purchaseOrder, items } = request;
    this.fastify.log.info(`Generating Gate Pass...`);
    const templatesPath = path.join(path.resolve(), 'src', 'templates');
    let gatePassNumber = 1;

    const latestGatePass = await this.gatePassRepository.findOne({
      order: [['id', 'DESC']],
    });

    if (latestGatePass) {
      gatePassNumber = latestGatePass.gatePassNumber + 1;
    }

    const outputPath = `./upload/GatePass/Gate Pass-PO-${purchaseOrder?.requisition?.companyCode}${purchaseOrder.purchaseOrder.poLetter}${purchaseOrder.purchaseOrder.poNumber}.pdf`;

    if (!fs.existsSync(path.dirname(outputPath))) {
      fs.mkdirSync(path.dirname(outputPath), { recursive: true });
    }
    const baseTemplatePath = path.join(templatesPath, `gatePassTemplate.pdf`);

    const existingPdfBytes = fs.readFileSync(baseTemplatePath);

    const gatePassPDF = await this.fillTemplate(
      existingPdfBytes,
      items.data,
      gatePassNumber,
      purchaseOrder,
    );

    fs.writeFileSync(outputPath, gatePassPDF);

    await this.createAttachment({
      userFromToken,
      requisitionId: purchaseOrder.requisition.id,
      body: {
        path: `Gate Pass-PO-${purchaseOrder?.requisition?.companyCode}${purchaseOrder.purchaseOrder.poLetter}${purchaseOrder.purchaseOrder.poNumber}.pdf`,
        name: `Gate Pass-PO-${purchaseOrder?.requisition?.companyCode}${purchaseOrder.purchaseOrder.poLetter}${purchaseOrder.purchaseOrder.poNumber}.pdf`,
      },
    });

    await this.gatePassRepository.create({
      poId: purchaseOrder.id,
      requisitionId: purchaseOrder.requisition.id,
      gatePassNumber,
    });

    return this.fastify.log.info(
      `Successfully Created Gate Pass for ${purchaseOrder.purchaseOrder.poNumber}`,
    );
  }

  async generateGatePassWithTemplate(request) {
    const { userFromToken, purchaseOrder, items } = request;
    this.fastify.log.info(`Generating Gate Pass with Template...`);

    // Get next gate pass number
    let gatePassNumber = 1;
    const latestGatePass = await this.gatePassRepository.findOne({
      order: [['id', 'DESC']],
    });

    if (latestGatePass) {
      gatePassNumber = latestGatePass.gatePassNumber + 1;
    }

    // Prepare data for template
    const gatePassData = {
      gatePassNumber,
      supplierName:
        purchaseOrder?.purchaseOrder?.supplier?.name ||
        purchaseOrder?.purchaseOrder?.supplierName ||
        'Unknown Supplier',
      companyName: purchaseOrder?.requisition?.company?.name || 'COMPANY NAME',
      items: items,
    };

    // Generate PDF using template service
    const pdfResult =
      await this.templateService.createGatePassPdf(gatePassData);

    // Save gate pass record
    await this.gatePassRepository.create({
      poId: purchaseOrder.id,
      requisitionId: purchaseOrder.requisition.id,
      gatePassNumber,
    });

    this.fastify.log.info(
      `Successfully Created Gate Pass with Template for ${purchaseOrder.purchaseOrder.poNumber}`,
    );

    return pdfResult;
  }
}

module.exports = GatePassService;
