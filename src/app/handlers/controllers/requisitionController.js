class Requisition {
  constructor(container) {
    const {
      canvassItemRepository,
      db,
      companyRepository,
      departmentRepository,
      clientErrors,
      projectRepository,
      leaveRepository,
      requisitionRepository,
      fastify,
      requisitionService,
      entities,
      requisitionItemListRepository,
      tomItemRepository,
      attachmentRepository,
      commentRepository,
      utils,
      requisitionApproverRepository,
      requisitionApproverService,
      constants,
      notificationService,
      noteService,
      userRepository,
      canvassRequisitionRepository,
      requisitionBadgeRepository,
      leaveService,
      requisitionCanvassHistoryRepository,
      requisitionOrderHistoryRepository,
      requisitionDeliveryHistoryRepository,
      requisitionPaymentHistoryRepository,
      requisitionReturnHistoryRepository,
      requisitionItemHistoryRepository,
      requisitionInvoiceHistoryRepository,
      purchaseOrderService,
      deliveryReceiptService,
      steelbarsRepository,
      rsPaymentRequestRepository,
      purchaseOrderCancelledItemsRepository,
      attachmentBadgeRepository,
      invoiceReportService,
      canvassService,
      templateService,
      downloadService,
      approverService,
      requisitionItemListService,
    } = container;

    this.canvassService = canvassService;
    this.canvassItemRepository = canvassItemRepository;
    this.db = db;
    this.Sequelize = db.Sequelize;
    this.companyRepository = companyRepository;
    this.departmentRepository = departmentRepository;
    this.attachmentRepository = attachmentRepository;
    this.clientErrors = clientErrors;
    this.projectRepository = projectRepository;
    this.leaveRepository = leaveRepository;
    this.requisitionRepository = requisitionRepository;
    this.fastify = fastify;
    this.requisitionService = requisitionService;
    this.requisitionEntity = entities.requisition;
    this.commentRepository = commentRepository;
    this.requisitionItemListEntity = entities.requisitionItemList;
    this.requisitionItemListRepository = requisitionItemListRepository;
    this.tomItemRepository = tomItemRepository;
    this.utils = utils;
    this.requisitionApproverRepository = requisitionApproverRepository;
    this.requisitionApproverService = requisitionApproverService;
    this.constants = constants;
    this.notificationService = notificationService;
    this.noteService = noteService;
    this.userRepository = userRepository;
    this.canvassRequisitionRepository = canvassRequisitionRepository;
    this.requisitionBadgeRepository = requisitionBadgeRepository;
    this.leaveService = leaveService;
    this.requisitionCanvassHistoryRepository =
      requisitionCanvassHistoryRepository;
    this.requisitionHistoryEntity = entities.requisitionHistory;
    this.requisitionOrderHistoryRepository = requisitionOrderHistoryRepository;
    this.requisitionDeliveryHistoryRepository =
      requisitionDeliveryHistoryRepository;
    this.requisitionPaymentHistoryRepository =
      requisitionPaymentHistoryRepository;
    this.requisitionReturnHistoryRepository =
      requisitionReturnHistoryRepository;
    this.requisitionItemHistoryRepository = requisitionItemHistoryRepository;
    this.requisitionInvoiceHistoryRepository =
      requisitionInvoiceHistoryRepository;
    this.purchaseOrderService = purchaseOrderService;
    this.deliveryReceiptService = deliveryReceiptService;
    this.steelbarsRepository = steelbarsRepository;
    this.entities = entities;
    this.rsPaymentRequestRepository = rsPaymentRequestRepository;
    this.purchaseOrderCancelledItemsRepository =
      purchaseOrderCancelledItemsRepository;
    this.attachmentBadgeRepository = attachmentBadgeRepository;
    this.invoiceReportService = invoiceReportService;
    this.templateService = templateService;
    this.templateService = templateService;
    this.downloadService = downloadService;
    this.approverService = approverService;
    this.requisitionItemListService = requisitionItemListService;
  }

  async createRequisition(request, reply) {
    this.fastify.log.info(`Initiating Creation of Requisition Slip`);
    const { body, userFromToken, params, counter = false } = request;
    const {
      departmentId,
      chargeTo,
      chargeToId,
      comment,
      isDraft = true,
      projectId,
      companyId,
      category,
      itemList,
    } = body;
    const { REQUISITION_STATUS } = this.constants.requisition;
    const { MODELS, USER_TYPES, COMMENT_TYPES } = this.constants.note;

    const numberField = isDraft === true ? 'draftRsNumber' : 'rsNumber';

    // validation for engineer user types
    this.validationRequisitionCreatedByEngineers(
      userFromToken,
      request.body.type,
    );

    if (!(await this.departmentRepository.getById(body.departmentId))) {
      throw this.clientErrors.NOT_FOUND({ message: 'Department not found' });
    }

    if (
      projectId &&
      !(await this.projectRepository.getById(projectId)) &&
      (body.type === 'ofm' || body.type === 'non-ofm')
    ) {
      throw this.clientErrors.NOT_FOUND({ message: 'Project not found' });
    }

    if (chargeTo == '') {
      delete body.chargeTo;
    }

    if (chargeToId == '') {
      delete body.chargeToId;
    }

    if (projectId == '') {
      delete body.projectId;
    }

    if (chargeTo && chargeToId === undefined) {
      body.chargeToId = this.requisitionService.parseChargeToId({
        chargeTo,
        companyId,
        projectId,
      });
    }

    if (
      chargeTo &&
      chargeTo !== '' &&
      chargeToId &&
      chargeToId !== '' &&
      !(await this.requisitionService.isValidChargeTo({ chargeTo, chargeToId }))
    ) {
      throw this.clientErrors.NOT_FOUND({
        message: `Charge to ${chargeTo} with id of ${chargeToId} not found`,
      });
    }

    const company = await this.companyRepository.getByCompanyId(body.companyId);
    if (!company) {
      throw this.clientErrors.NOT_FOUND({ message: 'Company not found' });
    }

    const { rsNumber, rsLetter } =
      await this.requisitionService.generateRSNumberCode(company.code, isDraft);

    body[numberField] = rsNumber;
    body.rsLetter = rsLetter;
    body.companyCode = company
      ? this.requisitionService.formatCompanyCode(company.code.toString())
      : '';
    body.createdBy = userFromToken.id;
    body.deliveryAddress = body.deliverTo;
    body.status =
      isDraft === true
        ? REQUISITION_STATUS.DRAFT
        : REQUISITION_STATUS.SUBMITTED;
    // body.status = 'draft';

    let requisition;
    let attachment;
    let itemLists;

    const transaction = await this.db.sequelize.transaction();

    try {
      // try { // for degging will refactor tom
      requisition = await this.requisitionRepository.create(body, {
        transaction,
      });
      // } catch (error) {
      //   console.log(error);
      // }

      const rsApprovers = await this.requisitionService.rsApproversV2({
        category,
        projectId,
        departmentId,
        userFromToken,
        transaction,
        companyId,
        itemList,
      });

      await this.requisitionService.assignRSApprovers({
        rsApprovers,
        category,
        requisitionId: requisition.id,
        transaction,
      });

      body.requisitionId = requisition.id;
      params.requisitionId = requisition.id;
    } catch (error) {
      this.fastify.log.info(
        `Requisition create error - ${JSON.stringify(error)}`,
      );

      await transaction.rollback();

      throw this.clientErrors.BAD_REQUEST({
        message: 'Requisition creation failed',
      });
    }

    attachment = await this.requisitionService.createAttachments(
      {
        userFromToken: request.userFromToken,
        body: { ...body },
      },
      transaction,
    );

    if (comment) {
      await this.noteService.createNote(
        {
          model: MODELS.REQUISITION,
          modelId: body.requisitionId,
          userName: userFromToken.fullNameUser,
          userType: USER_TYPES.REQUESTOR,
          commentType: COMMENT_TYPES.NOTE,
          note: body.comment,
        },
        { transaction },
      );
    }

    this.fastify.uploadLimit({
      route: 'requisition',
    });

    try {
      if (requisition.type === 'transfer') {
        request.body.itemList = await this.requisitionService.createTomItem({
          ...request,
          requisitionId: requisition.id,
          transaction,
        });

        itemLists = await this.requisitionService.createItemList(
          request,
          transaction,
        );
      } else {
        itemLists = await this.requisitionService.createItemList(
          request,
          transaction,
        );
      }
    } catch (error) {
      await transaction.rollback();
      this.fastify.log.info(
        `Failed to create item list: ${JSON.stringify(error)}`,
      );
      throw this.clientErrors.BAD_REQUEST({
        message: 'Failed to create item list',
      });
    }

    await transaction.commit();
    return counter === false
      ? reply.status(200).send({
          message: `Requisition ID: ${requisition.id} created successfully`,
          data: {
            requisition,
            attachment,
            comment,
            itemLists,
          },
        })
      : {
          message: `Requisition ID: ${requisition.id} created successfully`,
          data: {
            requisition,
            attachment,
            comment,
            itemLists,
          },
        };
  }

  async submitRequisition(request, reply) {
    const { counter, requisitionId, userId, isDraft, itemList } = request.body;
    const { MODELS, USER_TYPES, COMMENT_TYPES } = this.constants.note;

    const { REQUISITION_STATUS } = this.constants.requisition;

    const requisition = await this.requisitionRepository.getById(requisitionId);

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({ message: 'Requisition not found' });
    }

    if (requisition.status === REQUISITION_STATUS.REJECTED) {
      await this.approverService.resubmitApprover({
        model: 'requisition',
        modelId: requisitionId,
        userId,
      });
    }

    if (request.body.comment) {
      await this.noteService.createNote({
        model: MODELS.REQUISITION,
        modelId: requisitionId,
        userName: request.userFromToken.fullNameUser,
        userType: USER_TYPES.REQUESTOR,
        commentType: COMMENT_TYPES.NOTE,
        note: request.body.comment,
      });
    }

    if (request.body.attachments) {
      await this.requisitionService.createAttachments({
        userFromToken: request.userFromToken,
        body: { ...request.body },
      });
    }

    const { rsNumber } = await this.requisitionService.generateRSNumberCode(
      requisition.companyCode,
      false,
    );

    if (!request.body.projectId) {
      delete request.body.projectId;
    }

    if (itemList && itemList.length > 0) {
      this.fastify.log.info('Creating Item List');
      await this.requisitionService.updateItemList(
        requisitionId,
        itemList,
        isDraft,
      );
    }

    await this.requisitionRepository.update(
      { id: requisitionId },
      {
        ...request.body,
        status: REQUISITION_STATUS.SUBMITTED,
        rsNumber,
        draftRsNumber: null,
      },
    );

    return counter === false
      ? reply.status(200).send({
          message: `Requisition with id ${requisitionId} submitted successfully`,
        })
      : {
          message: `Requisition with id ${requisitionId} submitted successfully`,
        };
  }

  async getRequisitions(request, reply) {
    const { userFromToken } = request;
    const { sortBy, filterBy, ...queries } = request.query;
    const { requisitionSortSchema, requisitionFilterSchema } =
      this.requisitionEntity;
    const parsedSortBy = requisitionSortSchema.parse(sortBy);
    const parsedFilterBy = requisitionFilterSchema.parse(filterBy);

    const requisitions = await this.requisitionRepository.getAllRequisitions({
      ...queries,
      order: parsedSortBy,
      filterBy: parsedFilterBy,
      userId: userFromToken.id,
    });

    return reply.status(200).send({ ...requisitions });
  }

  async getRequisitionsV2(request, reply) {
    const { userFromToken } = request;
    const { sortBy, filterBy, ...queries } = request.query;

    const { requisitionSortSchema, requisitionFilterSchema } =
      this.requisitionEntity;

    const parsedSortBy = requisitionSortSchema.parse(sortBy);
    const parsedFilterBy = requisitionFilterSchema.parse(filterBy);

    const requisitions = await this.requisitionRepository.getAllRequisitionsV2({
      ...queries,
      order: parsedSortBy,
      filterBy: parsedFilterBy,
      userFromToken,
    });

    return reply.status(200).send({ ...requisitions });
  }

  async getRequisitionById(request, reply) {
    const { userFromToken } = request;
    const { requisitionId } = request.params;
    const { PO_STATUS } = this.constants.purchaseOrder;

    const today = new Date(new Date().setHours(0, 0, 0, 0));

    const local = new Date(
      today.getTime() - today.getTimezoneOffset() * 60 * 1000,
    );

    const requisition = await this.requisitionRepository.findOne({
      where: { id: requisitionId },
      attributes: {
        exclude: ['createdBy', 'companyId', 'departmentId', 'projectId'],
      },
      include: [
        {
          model: this.db.companyModel,
          attributes: [
            'id',
            [
              this.Sequelize.fn(
                'CONCAT',
                this.Sequelize.col('company.initial'),
                ' - ',
                this.Sequelize.col('company.name'),
              ),
              'name',
            ],
          ],
          as: 'company',
        },
        {
          model: this.db.projectModel,
          attributes: ['id', 'name'],
          as: 'project',
        },
        {
          model: this.db.departmentModel,
          attributes: ['id', 'name'],
          as: 'department',
        },
        {
          model: this.db.userModel,
          attributes: [
            'id',
            [
              this.Sequelize.fn(
                'CONCAT',
                this.Sequelize.col('first_name'),
                ' ',
                this.Sequelize.col('last_name'),
              ),
              'fullName',
            ],
          ],
          as: 'createdByUser',
        },
        {
          association: 'canvassRequisitions',
          as: 'canvass',
        },
        {
          association: 'purchaseOrders',
          attributes: ['id', 'poNumber', 'status'],
          required: false,
          where: {
            status: {
              [this.db.Sequelize.Op.in]: [
                PO_STATUS.FOR_PO_APPROVAL,
                PO_STATUS.FOR_PO_REVIEW,
                PO_STATUS.REJECT_PO,
              ],
            },
          },
          include: [
            {
              association: 'purchaseOrderApprovers',
              attributes: ['id', 'roleId', 'userId', 'isAdhoc'],
            },
          ],
        },
      ],
    });

    if (requisition.assignedTo !== null) {
      const { id, firstName, lastName } = await this.userRepository.getUserById(
        requisition.assignedTo,
      );

      requisition.assignedTo = {
        id: id,
        name: `${firstName} ${lastName}`,
      };
    }

    requisition.noteBadge = await this.requisitionService.commentBadge(
      request.userFromToken.id,
      requisition.id,
    );

    requisition.attachmentBadge = await this.requisitionService.attachmentBadge(
      request.userFromToken.id,
      requisition.id,
    );

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({ message: 'Requisition not found' });
    }

    const getItemListsWithSteelbars = async (itemLists) => {
      if (!itemLists) return [];

      const items = Array.isArray(itemLists) ? itemLists : itemLists.data || [];

      const enhancedItemLists = await Promise.all(
        items.map(async (item) => {
          if (item.item && item.item.acctCd) {
            const steelbar = await this.db.steelbarsModel.findOne({
              where: { ofm_acctcd: item.item.acctCd },
              attributes: [
                'grade',
                'diameter',
                'length',
                'weight',
                'kg_per_meter',
              ],
            });

            if (steelbar) {
              item.item.steelbarDetails = steelbar;
            }
          }
          return item;
        }),
      );

      if (!Array.isArray(itemLists) && itemLists.data) {
        return {
          ...itemLists,
          data: enhancedItemLists,
        };
      }

      return enhancedItemLists;
    };

    if (requisition.type === 'ofm') {
      const itemLists =
        await this.requisitionService.getOfmRequisitionItemLists(
          requisitionId,
          false,
        );
      requisition.requisitionItemLists =
        await getItemListsWithSteelbars(itemLists);
    }

    if (requisition.type === 'non-ofm') {
      requisition.requisitionItemLists =
        await this.requisitionService.getNonOfmRequisitionItemLists(
          requisitionId,
          false,
        );
    }

    if (requisition.type === 'ofm-tom') {
      const itemLists =
        await this.requisitionService.getOfmRequisitionItemLists(
          requisitionId,
          false,
        );
      requisition.requisitionItemLists =
        await getItemListsWithSteelbars(itemLists);
    }

    if (requisition.type === 'non-ofm-tom') {
      requisition.requisitionItemLists =
        await this.requisitionService.getNonOfmRequisitionItemLists(
          requisitionId,
          false,
        );
    }

    if (requisition.type === 'transfer') {
      requisition.requisitionItemLists =
        await this.requisitionService.getTransferRequisitionItemLists(
          requisitionId,
          false,
        );
    }

    // include requisition approvers
    const requisitionApprovers =
      await this.requisitionApproverRepository.findAll({
        where: { requisitionId },
        include: [
          {
            model: this.db.userModel,
            attributes: [
              'id',
              'username',
              [
                this.Sequelize.fn(
                  'CONCAT',
                  this.Sequelize.col('approver.first_name'),
                  ' ',
                  this.Sequelize.col('approver.last_name'),
                ),
                'fullName',
              ],
            ],
            include: [
              {
                model: this.db.roleModel,
                attributes: ['id', 'name'],
                as: 'role',
              },
              {
                model: this.db.leaveModel,
                attributes: ['id', 'startDate', 'endDate', 'totalDays'],
                as: 'userLeaves',
                required: false,
                where: {
                  [this.Sequelize.Op.and]: [
                    {
                      startDate: {
                        [this.Sequelize.Op.lte]: local,
                      },
                    },
                    {
                      endDate: {
                        [this.Sequelize.Op.gte]: local,
                      },
                    },
                  ],
                },
              },
            ],
            as: 'approver',
          },
          {
            model: this.db.userModel,
            attributes: [
              'id',
              'username',
              [
                this.Sequelize.fn(
                  'CONCAT',
                  this.Sequelize.col('altApprover.first_name'),
                  ' ',
                  this.Sequelize.col('altApprover.last_name'),
                ),
                'fullName',
              ],
            ],
            include: [
              {
                model: this.db.roleModel,
                attributes: ['id', 'name'],
                as: 'role',
              },
            ],
            as: 'altApprover',
          },
        ],
        order: [
          ['modelType', 'desc'],
          ['level', 'asc'],
          ['createdAt', 'asc'],
        ],
      });

    const updatedApprovers = await Promise.all(
      requisitionApprovers?.data?.map(async (approver) => {
        let altApproverId = null;

        if (approver?.approver?.userLeaves.length) {
          const leave = approver?.approver?.userLeaves[0];
          const workFlows = await this.leaveService.getLeaveByPk(leave?.id);
          if (workFlows) {
            workFlows.requisitions.every((workflow) => {
              if (
                workflow?.altApproverId !== undefined &&
                workflow?.altApproverId !== null
              ) {
                altApproverId = workflow?.altApproverId;
                return false;
              }
              return true;
            });
          }
        }

        await this.requisitionApproverRepository.update(
          { requisitionId: requisition.id, approverId: approver.approverId },
          {
            altApproverId: altApproverId,
          },
        );

        const user = altApproverId
          ? await this.userRepository.getUserById(altApproverId)
          : null;
        return {
          ...approver,
          altApproverId: altApproverId,
          altApprover: altApproverId
            ? {
                id: user?.id,
                fullName: user?.fullNameUser,
                username: user?.username,
                role: {
                  id: user?.role?.id,
                  name: user?.role?.name,
                },
              }
            : null,
        };
      }),
    );

    requisition.requisitionApprovers = {
      data: updatedApprovers,
    };

    requisition.attachments = await this.requisitionService.getAttachments(
      (request = {
        ...request,
        id: requisitionId,
      }),
    );

    requisition.comment = await this.noteService.getLastNotes({
      model: 'requisition',
      modelId: requisitionId,
    });

    requisition.rsNumber =
      await this.requisitionService.formatRSNumber(requisition);

    const hasUnassignedInvoiceReport =
      await this.invoiceReportService.hasUnassignedInvoiceReportToPaymentRequest(
        requisition.id,
      );

    const setPurchaseOrderActions = (poStatus) => {
      if (requisition.purchaseOrders.length === 0) {
        return false;
      }

      const assignedApproverIsLoggedUser = requisition.purchaseOrders.some(
        (purchaseOrder) =>
          purchaseOrder.purchaseOrderApprovers.some(
            (approver) => approver.userId === userFromToken.id,
          ),
      );

      const assignedPurchasingStaffIsLoggedUser =
        requisition.assignedTo.id === userFromToken.id;

      const checkCorrectPOStatus = requisition.purchaseOrders.some(
        (purchaseOrder) => purchaseOrder.status === poStatus,
      );

      return (
        (assignedApproverIsLoggedUser && checkCorrectPOStatus) ||
        (assignedPurchasingStaffIsLoggedUser && checkCorrectPOStatus)
      );
    };

    const isRequestorOrAssignedPurchasingStaff =
      requisition.assignedTo?.id === userFromToken.id ||
      requisition.createdByUser?.id === userFromToken.id;
    const canCreateInvoice = await this.invoiceReportService.canCreateInvoice(
      requisition.id,
    );

    requisition.actions = {
      canEnterCanvass: requisition.assignedTo?.id === userFromToken.id,
      hasPOForReview: setPurchaseOrderActions(PO_STATUS.FOR_PO_REVIEW),
      hasPOForApproval: setPurchaseOrderActions(PO_STATUS.FOR_PO_APPROVAL),
      hasForDelivery:
        await this.purchaseOrderService.areSomePOItemsPartiallyDelivered(
          requisition.id,
        ),
      canCreateInvoice:
        isRequestorOrAssignedPurchasingStaff && canCreateInvoice,
      canCreatePaymentRequest:
        hasUnassignedInvoiceReport &&
        requisition.assignedTo?.id === userFromToken.id,
    };

    const cancelledPurchaseOrders =
      await this.purchaseOrderCancelledItemsRepository.getAllRSPurchaseOrderCancelled(
        requisitionId,
      );

    requisition.cancelledPurchaseOrders = cancelledPurchaseOrders;

    const canvassItemList = await this.canvassService.checkItemsValidity({
      canvassType: requisition.type,
      requisitionId,
      isSubmitting: false,
    });

    const hasItemsAvailableForCanvassing = canvassItemList?.some(
      (item) => item.requestedQty > item.canvassedQty,
    );

    requisition.hasItemsAvailableForCanvassing = hasItemsAvailableForCanvassing;

    return reply.status(200).send({ message: `Get Success`, requisition });
  }

  async getChargeToList(request, reply) {
    const { category } = request.query;
    const result = await this.requisitionService.getChargeToList(category);
    return reply.status(200).send({
      message: `Successfully Retrieved Charge to list of ${category}`,
      result,
    });
  }

  async getDeliveryAddresses(request, reply) {
    const deliveryAddresses =
      await this.requisitionService.getDeliveryAddresses();
    return reply.status(200).send({ ...deliveryAddresses });
  }

  async createRequisitionTomItemList(request, reply) {
    const { requisitionId } = request.params;
    const { body } = request;

    const requisition = await this.requisitionRepository.getById(requisitionId);

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({ message: 'Requisition not found' });
    }

    const tomItem = await this.tomItemRepository.create({
      ...body,
      requisitionId,
    });

    return reply.status(200).send({
      message: `Successfully Created Transfer of Material Item`,
      result: tomItem,
    });
  }

  async markCommentOrAttachmentAsSeen(request, reply) {
    const { model, requisitionId } = request.body;

    const modelData = await this.requisitionRepository.findOne({
      where: {
        id: requisitionId,
      },
      include: [
        {
          model: this.db[`${model}Model`],
          as: `${model}s`,
          required: true,
          include: [
            {
              model: this.db[`${model}BadgeModel`],
              as: `${model}Badge`,
            },
          ],
        },
      ],
    });

    if (!modelData) {
      throw this.clientErrors.NOT_FOUND({ message: `${model}s not found` });
    }

    const modelIds = modelData[`${model}s`].map((data) => data.id);

    await this[`${model}BadgeRepository`].bulkCreate(
      modelIds.map((id) => ({
        userId: request.userFromToken.id,
        [`${model}Id`]: id,
      })),
    );

    return reply
      .status(200)
      .send({ message: `${model} with id of ${requisitionId} marked as seen` });
  }

  async getRequisitionItems(request, reply) {
    const { sortBy, ...queries } = request.query;
    const { requisitionId } = request.params;
    const { requisitionItemListSortSchema } = this.requisitionItemListEntity;

    const requisition = await this.requisitionRepository.getById(requisitionId);

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({ message: 'Requisition not found' });
    }

    let requisitionItems =
      await this.requisitionItemListRepository.getAllRequisitionItems({
        ...queries,
        requisitionId,
        type: requisition.type,
        order: requisitionItemListSortSchema.parse(sortBy),
      });

    return reply.status(200).send(requisitionItems);
  }

  async updateRequisition(request, reply) {
    const { userFromToken } = request;
    const { MODELS, USER_TYPES, COMMENT_TYPES } = this.constants.note;
    const { REQUISITION_STATUS } = this.constants.requisition;

    const {
      itemList,
      isDraft,
      attachments,
      updateAttachments,
      comment,
      ...requisitionDetails
    } = request.body;
    const id = request.params.requisitionId;
    let requisition;

    const existingRequisition = await this.requisitionRepository.getById(id);

    if (
      userFromToken.id === existingRequisition.createdBy &&
      existingRequisition.status === REQUISITION_STATUS.SUBMITTED
    ) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Requisition is already submitted',
      });
    }

    // validation for engineer user types
    if (userFromToken.id === existingRequisition.createdBy) {
      this.validationRequisitionCreatedByEngineers(
        userFromToken,
        request.body.type,
      );
    }

    this.fastify.log.info(`Updating Requisition ID: ${id}`);
    this.fastify.log.info(`Request Body: ${JSON.stringify(request.body)}`);

    try {
      requisition = await this.requisitionRepository.findOne({
        where: { id },
        attributes: {
          exclude: ['createdBy', 'companyId', 'departmentId', 'projectId'],
        },
        include: [
          {
            model: this.db.companyModel,
            attributes: ['id', 'name'],
            as: 'company',
          },
          {
            model: this.db.projectModel,
            attributes: ['id', 'name'],
            as: 'project',
          },
          {
            model: this.db.departmentModel,
            attributes: ['id', 'name'],
            as: 'department',
          },
          {
            model: this.db.userModel,
            attributes: [
              'id',
              [
                this.Sequelize.fn(
                  'CONCAT',
                  this.Sequelize.col('first_name'),
                  ' ',
                  this.Sequelize.col('last_name'),
                ),
                'fullName',
              ],
            ],
            as: 'createdByUser',
          },
        ],
      });

      await this.requisitionService.updateRequisitionDetails(id, {
        ...requisitionDetails,
        deliveryAddress: requisitionDetails.deliverTo,
      });

      if (itemList && itemList.length > 0) {
        this.fastify.log.info('Updating item list');
        await this.requisitionService.updateItemList(id, itemList, isDraft);
      }

      if (!itemList || itemList.length == 0) {
        await this.requisitionService.deleteRequisitionItems(id, []);
      }

      if (comment) {
        await this.requisitionService.upsertRequisitionNote({
          model: MODELS.REQUISITION,
          modelId: parseInt(id),
          userName: userFromToken.fullNameUser,
          userType: USER_TYPES.REQUESTOR,
          commentType: COMMENT_TYPES.NOTE,
          note: comment,
        });
      }

      if (attachments || updateAttachments) {
        await this.requisitionService.updateRequisitionAttachments({
          userFromToken,
          attachments,
          updateAttachments,
          requisitionId: id,
        });
      }
    } catch (error) {
      this.fastify.log.error(
        `[Error] Updating Requisition: ${error.message || JSON.stringify(error)}`,
      );
      throw this.clientErrors.BAD_REQUEST({
        message: error.message || 'Error updating requisition',
      });
    }

    return reply.status(200).send({
      message: `Successfully updated Requisition ID: ${id}`,
      previousValue: requisition,
    });
  }

  async createRequisitionAttachments(request, reply) {
    request.body = {
      ...request.params,
      ...request.body,
    };

    const result = await this.requisitionService.createAttachments(request);
    return reply.status(200).send({
      message: `Attachments Successfully attached to Requisitions`,
      result,
    });
  }

  async getRequisitionAttachments(request, reply) {
    const { id } = request.params;

    const payload = {
      ...request,
      id,
    };

    const result = await this.requisitionService.getAttachments(payload);
    return reply.status(200).send({
      message: `Requisition ID ${id} attachments`,
      result,
    });
  }

  async createRequisitionComment(request, reply) {
    request.body = {
      ...request.params,
      ...request.body,
    };

    const result = await this.requisitionService.createComment(request);

    return reply.status(200).send({
      message: `Successfully attached comment to Requisition Slip`,
      result,
    });
  }

  async getRequisitionComments(request, reply) {
    const { id } = request.params;

    const payload = {
      ...request,
      id,
    };
    const result = await this.requisitionService.getComments(payload);

    return reply.status(200).send({
      message: `Requisition ID ${id} comments`,
      result,
    });
  }

  /**
   * Approves a requisition by updating requistion_approvers status in the database.
   * This function checks if the requisition exists and if the user is authorized to approve it.
   * If approved, it updates the requisition approver's status and sets the requisition status to assigning.
   * And notifies the requester through the notification bell.
   *
   * if additional approver is added, it will notify the additional approver
   *
   * @param {Object} request - The request object containing requisitionId and user information.
   * @param {Object} reply - The reply object used to send the response.
   */
  async approveRequisition(request, reply) {
    const { userFromToken } = request;
    const { requisitionId } = request.params;
    const { NOTIFICATION_TYPES } = this.constants.notification;
    const { REQUISITION_STATUS } = this.constants.requisition;
    const { MODELS, USER_TYPES, COMMENT_TYPES } = this.constants.note;
    const approverId = request.body?.approverId || null;
    const comment = request.body?.comment || null;
    const itemList = request.body?.itemList || null;

    const requisition = await this.requisitionRepository.getById(requisitionId);

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({ message: 'Requisition not found' });
    }

    if (requisition.status === REQUISITION_STATUS.REJECTED) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Adding approvers is not allowed for rejected requisition',
      });
    }

    if (['rejected', 'cancelled'].includes(requisition.status)) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Unable to update rejected or cancelled requisition',
      });
    }

    let rsToApprove = await this.requisitionApproverRepository.findOne({
      where: {
        requisitionId,
        approverId: userFromToken.id,
        status: 'pending',
        level: 0,
      },
    });

    if (!rsToApprove) {
      rsToApprove = await this.requisitionApproverRepository.findOne({
        where: {
          requisitionId,
          approverId: userFromToken.id,
          status: 'pending',
        },
        order: [['approverLevel', 'ASC']],
      });
    }
    // Check if user is an alternative approver
    if (!rsToApprove) {
      rsToApprove = await this.requisitionApproverRepository.findOne({
        where: { requisitionId, altApproverId: userFromToken.id },
        order: [['approverLevel', 'ASC']],
      });
    }

    if (!rsToApprove) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'You are not part of the approver for this RS',
      });
    }

    if (rsToApprove.status === 'approved') {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Requisition already approved',
      });
    }

    if (
      await this.requisitionApproverService.approvedByPreviousApprover(
        rsToApprove,
        requisition,
      )
    ) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Current approver not yet approved the requisition!',
      });
    }

    const transaction = await this.db.sequelize.transaction();
    try {
      await this.approverService.overrideApprover({
        model: 'requisition',
        modelId: requisitionId,
        approverId: userFromToken.id,
        transaction,
      });

      await this.requisitionApproverRepository.update(
        { id: rsToApprove.id },
        { status: 'approved' },
        { transaction },
      );

      const fullyApproved =
        await this.requisitionApproverService.setRSStatusToAssigningWhenFullyApproved(
          {
            requisitionId,
            transaction,
            approverId,
          },
        );

      if (fullyApproved) {
        const rsNumber =
          await this.requisitionService.formatRSNumber(requisition);
        await this.notificationService.sendNotification({
          transaction,
          title: 'Slip Approved',
          message: `Submitted Requisition Slip with RS Number of ${rsNumber} has been
          Approved by all of the Approvers. A Purchasing or Procurement Staff will
          be assigned to proceed with the Request Flow`,
          type: NOTIFICATION_TYPES.REQUISITION_SLIP,
          recipientUserIds: [requisition.createdBy],
          senderId: userFromToken.id,
          metaData: {
            requisitionId: requisition.id,
          },
        });
      }

      if (approverId) {
        await this.requisitionApproverService.addAdditionalApprover({
          approverId,
          requisitionId,
          transaction,
          approverLevel: String(rsToApprove.level),
          userId: userFromToken.id,
          rsToApprove,
        });

        await this.notificationService.sendAdditionalApproverNotification({
          transaction,
          userFromToken,
          approverId,
          requisitionId,
        });
      }

      if (comment) {
        await this.noteService.createNote({
          model: MODELS.REQUISITION,
          modelId: parseInt(requisitionId),
          userName: userFromToken.fullNameUser,
          userType: USER_TYPES.APPROVER,
          commentType: COMMENT_TYPES.APPROVAL,
          note: comment,
        });
      }

      if (itemList) {
        await this.requisitionService.updateItemQuantityWithApprover({
          requisition,
          itemList,
          transaction,
        });
      }

      await transaction.commit();

      return reply.status(200).send({
        message: 'Requisition approved successfully',
        previousValue: { status: 'submitted' },
        newValue: { status: 'approved' },
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Rejects a requisition by updating requistion_approvers status in the database.
   * This function checks if the requisition exists and if the user is authorized to reject it.
   * If rejected, it updates the requisition approver's status and sets the requisition status to rejected.
   * And notifies the requester through the notification bell.
   * And adds a comment to the comments table.
   *
   * * if additional approver is added, it will notify the additional approver
   *
   * @param {Object} request - The request object containing requisitionId and user information.
   * @param {Object} reply - The reply object used to send the response.
   */
  async rejectRequisition(request, reply) {
    const { userFromToken } = request;
    const { requisitionId } = request.params;
    const { MODELS, USER_TYPES, COMMENT_TYPES } = this.constants.note;
    const { NOTIFICATION_TYPES } = this.constants.notification;
    const { REQUISITION_STATUS } = this.constants.requisition;
    const { comments } = request.body;
    const approverId = request.body?.approverId || null;

    const requisition = await this.requisitionRepository.getById(requisitionId);

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({ message: 'Requisition not found' });
    }

    if (requisition.status === REQUISITION_STATUS.REJECTED) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Unable to reject already rejected requisition',
      });
    }

    const rsToApprove = await this.requisitionApproverRepository.findOne({
      where: {
        requisitionId,
        approverId: userFromToken.id,
        status: 'pending',
      },
      order: [['approverLevel', 'ASC']],
    });

    if (!rsToApprove) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Unable to find requisition approver',
      });
    }

    if (rsToApprove.status === 'rejected') {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Requisition already rejected',
      });
    }

    if (['rejected', 'cancelled'].includes(requisition.status)) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Unable to update rejected or cancelled requisition',
      });
    }

    const transaction = await this.db.sequelize.transaction();
    try {
      await this.approverService.overrideApprover({
        model: 'requisition',
        modelId: requisitionId,
        approverId: userFromToken.id,
        status: 'rejected',
        transaction,
      });

      await this.requisitionApproverRepository.update(
        { id: rsToApprove.id },
        { status: 'rejected' },
        { transaction },
      );

      await this.requisitionApproverService.setRSStatusToRejected(
        requisitionId,
        transaction,
      );

      await this.noteService.createNote({
        model: MODELS.REQUISITION,
        modelId: parseInt(requisitionId, 10),
        userName: userFromToken.fullNameUser,
        userType: USER_TYPES.APPROVER,
        commentType: COMMENT_TYPES.DISAPPROVAL,
        note: comments.length ? comments[0].comment : '',
      });

      await this.notificationService.sendNotification({
        transaction,
        title: 'Slip Rejected',
        message: `Requisition Slip has been Rejected by one of the Approvers.
        Click here or access the Dashboard to proceed in reviewing the Requisition Slip`,
        type: NOTIFICATION_TYPES.REQUISITION_SLIP,
        recipientUserIds: [requisition.createdBy],
        senderId: userFromToken.id,
        metaData: {
          requisitionId: requisition.id,
        },
      });

      // will refactor after demo
      if (approverId) {
        await this.requisitionApproverService.addAdditionalApprover({
          approverId,
          requisitionId,
          transaction,
          approverLevel: String(rsToApprove.level),
        });
        await this.notificationService.sendAdditionalApproverNotification({
          transaction,
          userFromToken,
          approverId,
          requisitionId,
        });
      }

      await this.requisitionItemListService.revertItemQuantity({
        requisition,
        transaction,
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }

    return reply.status(200).send({
      message: 'Requisition rejected successfully',
      previousValue: { status: 'submitted' },
      newValue: { status: 'rejected' },
    });
  }

  async assignRequisition(request, reply) {
    const {
      userFromToken,
      params: { requisitionId },
      body: { userId, type } = {},
    } = request;

    let previousValue;

    this.fastify.log.info(`Assigning Requisition ID: ${requisitionId}`);

    try {
      previousValue =
        await this.requisitionApproverService.getRequisitionApprovers(request);
      await this.requisitionService.assignRequisition(
        requisitionId,
        userId || userFromToken.id,
        type || 'assign',
        userFromToken.id,
      );
    } catch (error) {
      throw error;
    }

    return reply.status(200).send({
      message: `Successfully assigned Requisition ID: ${requisitionId}`,
      previousValue,
    });
  }

  async getCanvassRequisition(request, reply) {
    const { requisitionId } = request.params;
    const { page, limit, sortBy, filterBy } = request.query;

    const requisition = await this.requisitionRepository.getCanvassRequisition(
      parseInt(requisitionId),
      { page, limit, sortBy, filterBy },
    );

    if (!requisition && !Object.keys(JSON.parse(filterBy)).length) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Requisition not found',
      });
    }

    return reply.status(200).send(requisition);
  }

  async cancelRequisition(request, reply) {
    const { requisitionId } = request.params;

    this.fastify.log.info(`Cancelling Requisition ID: ${requisitionId}`);

    try {
      await this.requisitionRepository.cancelRequisition(request);

      return reply.status(200).send({
        message: `Successfully cancelled Requisition ID: ${requisitionId}`,
      });
    } catch (error) {
      throw error;
    }
  }

  async seenBadge(request, reply) {
    const userId = request.userFromToken.id;

    const rsBadges = await this.requisitionBadgeRepository.findAll({
      attributes: ['id', 'seenBy'],
      where: {
        [this.Sequelize.Op.not]: {
          seenBy: {
            [this.Sequelize.Op.contains]: [userId],
          },
        },
      },
      paginate: false,
    });

    if (rsBadges.total === 0) {
      throw this.clientErrors.NOT_FOUND({ message: 'Nothing to update' });
    }

    rsBadges.data.map((badge) => {
      this.requisitionBadgeRepository.update(
        { id: badge.id },
        { seenBy: [...badge.seenBy, userId] },
      );
    });

    return reply.status(200).send({
      message: 'Badge seen successfully',
    });
  }

  async getCanvassItemList(request, reply) {
    const { canvassRequisitionId } = request.query;
    const { requisitionId } = request.params;
    const requisition = await this.requisitionRepository.getById(requisitionId);
    const canvassId =
      requisition?.canvassRequisitions?.id ?? canvassRequisitionId;

    const rsItemList =
      await this.requisitionItemListRepository.getRequisitionAddItems(
        requisition.id,
      );

    let canvassItemData = [];
    if (canvassId) {
      const canvassItems = await this.canvassItemRepository.findAll({
        paginate: false,
        where: { canvassRequisitionId: canvassId },
        include: [
          {
            association: 'suppliers',
            as: 'suppliers',
            required: false,
          },
        ],
      });

      canvassItemData = canvassItems.data;
    }

    const { type: canvassType } = requisition;
    const canvassItemList = await this.canvassService.checkItemsValidity({
      canvassType,
      requisitionId,
      isSubmitting: false,
    });

    const enhancedData = await Promise.all(
      rsItemList.data.map(async (item) => {
        const matchingCanvassItem = canvassItemData.filter(
          (canvassItem) => canvassItem.requisitionItemListId === item.id,
        );

        const canvassItem = canvassItemList.find(
          (canvassItem) => canvassItem.itemId === item.itemId,
        );

        const isEligibleForCanvassing =
          canvassItem && canvassItem.remainingQty > 0;

        if (item.item && item.item.acctCd) {
          const steelbarDetails =
            await this.steelbarsRepository.getSteelbarByAcctCd(
              item.item.acctCd,
            );
          if (steelbarDetails) {
            item.item.steelbarDetails = steelbarDetails;
          }
        }

        return {
          ...item,
          isEligibleForCanvassing,
          isSelected: !!matchingCanvassItem.length,
        };
      }),
    );

    return reply.status(200).send({
      ...rsItemList,
      data: enhancedData,
    });
  }

  async getRequisitionHistory(request, reply) {
    const { sortBy, ...queries } = request.query;
    const type =
      request.query.type.charAt(0).toUpperCase() + request.query.type.slice(1);
    const { requisitionId } = request.params;
    const { rsHistorySortSchema } = this.requisitionHistoryEntity;

    const history = await this[
      `requisition${type}HistoryRepository`
    ].getRequisitionHistory({
      requisitionId,
      sort: rsHistorySortSchema.parse(sortBy),
      ...queries,
    });
    return reply.status(200).send(history);
  }

  validationRequisitionCreatedByEngineers(userFromToken, requisitionType) {
    const { USER_TYPES: USER_ROLE_TYPE } = this.constants.user;

    if (
      (userFromToken.role.name !== USER_ROLE_TYPE.ENGINEERS &&
        requisitionType === 'ofm') ||
      (userFromToken.role.name !== USER_ROLE_TYPE.ENGINEERS &&
        requisitionType === 'ofm-tom')
    ) {
      throw this.clientErrors.BAD_REQUEST({
        message:
          'Only engineers can submit requisition type OFM and OFM Transfer of Materials',
      });
    }
  }

  async submit(request, reply) {
    const { body, userFromToken } = request;
    const { attachments, ...restBody } = { ...body };
    const parsedBody = await this.utils.parseDomain(
      this.entities.requisition.submitRequisition,
      restBody,
    );

    if (parsedBody.isDraft === 'false' && parsedBody?.id !== undefined) {
      // validation for engineer user types
      this.validationRequisitionCreatedByEngineers(
        userFromToken,
        request.body.type,
      );

      return reply.status(200).send(
        await this.submitRequisition({
          userFromToken: request.userFromToken,
          body: {
            ...parsedBody,
            requisitionId: parsedBody.id,
            counter: true,
            attachments,
            userId: userFromToken.id,
            userFromToken,
          },
        }),
      );
    }

    return reply
      .status(200)
      .send(await this.createRequisition({ ...request, counter: true }));
  }

  async closeRequisition(request, reply) {
    const { id: paymentRequestId } = request.params;
    const { RS_PAYMENT_REQUEST_STATUS } = this.constants.rsPaymentRequest;

    const rsPaymentRequest =
      await this.rsPaymentRequestRepository.getById(paymentRequestId);

    if (!rsPaymentRequest) {
      throw this.clientErrors.NOT_FOUND({
        message: `Payment request not found with ID ${paymentRequestId}`,
      });
    }

    if (rsPaymentRequest.status !== RS_PAYMENT_REQUEST_STATUS.APPROVED) {
      return;
    }
    const { requisitionId } = rsPaymentRequest;

    return await this.requisitionService.closeRequisition(requisitionId);
  }

  async getOptions(request, reply) {
    const options = await this.requisitionService.getOptions();
    return reply.status(200).send(options);
  }

  async createDashboardPdf(request, reply) {
    this.fastify.log.info(`Generating requisition slip PDF...`);

    const { requisitionSortSchema, requisitionFilterSchema } =
      this.requisitionEntity;

    const { sortBy, filterBy, ...queries } = request.query;

    const parsedSortBy = requisitionSortSchema.parse(sortBy);
    const parsedFilterBy = requisitionFilterSchema.parse(filterBy);

    try {
      const result = await this.templateService.createDashboardPdf({
        sortBy: parsedSortBy,
        filterBy: parsedFilterBy,
        ...queries,
        userFromToken: request.userFromToken,
      });
      this.fastify.log.info(
        `PDF generated, sending response with ${result.pdfBytes.length} bytes`,
      );

      return reply
        .header('Content-Type', 'application/pdf')
        .header(
          'Content-Disposition',
          `attachment; filename="${result.fileName}"`,
        )
        .send(result.pdfBytes);
    } catch (error) {
      this.fastify.log.error(
        `Error in requisitionPDF controller: ${error.message}`,
      );
      throw error;
    }
  }

  async createRequisitionPdf(request, reply) {
    const { id: requisitionId } = request.params;

    this.fastify.log.info(`Generating  requisition slip PDF...`);

    try {
      const result =
        await this.templateService.createRequisitionPdf(requisitionId);
      this.fastify.log.info(
        `PDF generated, sending response with ${result.pdfBytes.length} bytes`,
      );

      return reply
        .header('Content-Type', 'application/pdf')
        .header(
          'Content-Disposition',
          `attachment; filename="${result.fileName}"`,
        )
        .send(result.pdfBytes);
    } catch (error) {
      this.fastify.log.error(
        `Error in requisitionPDF controller: ${error.message}`,
      );
      throw error;
    }
  }
}

module.exports = Requisition;
