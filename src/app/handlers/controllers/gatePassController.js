class GatePassController {
  constructor(container) {
    const {
      constants,
      db,
      utils,
      entities,
      fastify,
      clientErrors,
      rsPaymentRequestService,
      gatePassService,
    } = container;

    this.constants = constants;
    this.fastify = fastify;
    this.db = db;
    this.utils = utils;
    this.entities = entities;
    this.clientErrors = clientErrors;
    this.rsPaymentRequestService = rsPaymentRequestService;
    this.gatePassService = gatePassService;
  }

  async generateGatePass(request, reply) {
    const { userFromToken } = request;
    const paymentRequestId = parseInt(request.params.id);
    const { RS_PAYMENT_REQUEST_STATUS } = this.constants.rsPaymentRequest;

    try {
      const { rsPaymentRequest: existingPaymentRequest, items } =
        await this.rsPaymentRequestService.getRsPaymentRequestByPk(
          paymentRequestId,
        );

      //If PR is fully approved
      console.log(existingPaymentRequest);
      if (
        existingPaymentRequest.status !== RS_PAYMENT_REQUEST_STATUS.APPROVED
      ) {
        console.log(existingPaymentRequest.status);
        console.log(
          '---------------------------------- PR exit: not fully approved',
        );
        return;
      }

      //Validate if requisition linked to PR is non-ofm
      if (
        existingPaymentRequest.requisition.type === 'non-ofm' ||
        existingPaymentRequest.requisition.type === 'ofm'
      ) {
        console.log(
          '---------------------------------- PR exit: type is non ofm or ofm',
        );
        return;
      }

      const pdfResult = await this.gatePassService.generateGatePassWithTemplate(
        {
          userFromToken,
          purchaseOrder: existingPaymentRequest,
          items,
        },
      );

      return reply.send({
        success: true,
        message: 'Gate pass generated successfully',
        data: pdfResult,
      });
    } catch (error) {
      throw error;
    }
  }
}

module.exports = GatePassController;
