const fs = require('node:fs/promises');
const fsSync = require('node:fs');
const path = require('path');

const HTML_FOLDER = path.join(__dirname, '../../../templates/html');
const PDF_FOLDER = path.join(__dirname, '../../../templates/pdf');

class Template {
  constructor(container) {
    const {
      templateService,
      fastify,
      clientErrors,
      rsPaymentRequestService,
      gatePassService,
      constants,
    } = container;

    this.fastify = fastify;
    this.templateService = templateService;
    this.clientErrors = clientErrors;
    this.rsPaymentRequestService = rsPaymentRequestService;
    this.gatePassService = gatePassService;
    this.constants = constants;
    this.handlebarsTemplateFolder = path.join(
      path.resolve(),
      'src',
      'templates',
      'hbs',
    );
  }

  async generateTemplate(request, reply) {
    this.fastify.log.info(`Generating HTML to PDF templates...`);

    try {
      await fs.access(HTML_FOLDER);
      const stats = await fs.stat(HTML_FOLDER);

      if (!stats.isDirectory()) {
        throw this.clientErrors.NOT_FOUND({
          message: `Input folder "${HTML_FOLDER}" is not a directory.`,
        });
      }

      if (!fsSync.existsSync(PDF_FOLDER)) {
        try {
          await fs.mkdir(PDF_FOLDER, { recursive: true });
        } catch (mkdirError) {
          console.error(
            `Error creating output folder "${PDF_FOLDER}":`,
            mkdirError,
          );
          reply.status(500).send({
            error: `Failed to create output folder: ${mkdirError.message}`,
          });
          return;
        }
      }

      const templates = await fs.readdir(HTML_FOLDER);

      if (templates.length === 0) {
        throw this.clientErrors.NOT_FOUND({
          message: `No Templates files found in HTML folder.`,
        });
      }

      await this.templateService.generateTemplate(
        templates,
        HTML_FOLDER,
        PDF_FOLDER,
      );

      return reply.status(200).send({
        meta: 'Successfully created PDF templates',
      });
    } catch (error) {
      throw error;
    }
  }

  async generateRsPaymentRequestPdf(request, reply) {
    const paymentRequestId = parseInt(request.params.id);

    try {
      const pdfResult =
        await this.templateService.createRsPaymentRequestPdf(paymentRequestId);

      return reply.send({
        success: true,
        message: 'Payment request PDF generated successfully',
        data: pdfResult,
      });
    } catch (error) {
      this.fastify.log.error('Error generating payment request PDF:', error);
      throw error;
    }
  }

  async generateGatePassPdf(request, reply) {
    const { userFromToken } = request;
    const paymentRequestId = parseInt(request.params.id);
    const { RS_PAYMENT_REQUEST_STATUS } =
      this.constants?.rsPaymentRequest || {};

    try {
      // Get payment request data (same logic as gatePassController)
      const { rsPaymentRequest: existingPaymentRequest, items } =
        await this.rsPaymentRequestService.getRsPaymentRequestByPk(
          paymentRequestId,
        );

      // Validation checks (same as gatePassController)
      if (
        RS_PAYMENT_REQUEST_STATUS &&
        existingPaymentRequest.status !== RS_PAYMENT_REQUEST_STATUS.APPROVED
      ) {
        return reply.status(400).send({
          success: false,
          message: 'Payment request is not fully approved',
        });
      }

      if (
        existingPaymentRequest.requisition.type === 'non-ofm' ||
        existingPaymentRequest.requisition.type === 'ofm'
      ) {
        return reply.status(400).send({
          success: false,
          message: 'Gate pass not applicable for this requisition type',
        });
      }

      // Generate gate pass using template service
      const pdfResult = await this.gatePassService.generateGatePassWithTemplate(
        {
          userFromToken,
          purchaseOrder: existingPaymentRequest,
          items,
        },
      );

      return reply.send({
        success: true,
        message: 'Gate pass PDF generated successfully',
        data: pdfResult,
      });
    } catch (error) {
      this.fastify.log.error('Error generating gate pass PDF:', error);
      throw error;
    }
  }
}

module.exports = Template;
